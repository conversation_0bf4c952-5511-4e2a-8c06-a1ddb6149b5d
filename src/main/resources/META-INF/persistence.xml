<?xml version="1.0" encoding="UTF-8"?>
<persistence xmlns="https://jakarta.ee/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence
             https://jakarta.ee/xml/ns/persistence/persistence_3_0.xsd"
             version="3.0">

    <persistence-unit name="trainTicketPU" transaction-type="RESOURCE_LOCAL">
        <provider>org.hibernate.jpa.HibernatePersistenceProvider</provider>

        <!-- Entités -->
        <class>com.trainticket.model.User</class>
        <class>com.trainticket.model.Train</class>
        <class>com.trainticket.model.Route</class>
        <class>com.trainticket.model.Booking</class>
        <class>com.trainticket.model.Ticket</class>
        <class>com.trainticket.model.Payment</class>
        <class>com.trainticket.model.Reservation</class>

        <exclude-unlisted-classes>true</exclude-unlisted-classes>

        <properties>
            <!-- Configuration MySQL avec XAMPP -->
            <property name="jakarta.persistence.jdbc.driver" value="com.mysql.cj.jdbc.Driver"/>
            <property name="jakarta.persistence.jdbc.url" value="*************************************************************************************************************************************************"/>
            <property name="jakarta.persistence.jdbc.user" value="root"/>
            <property name="jakarta.persistence.jdbc.password" value=""/>

            <!-- Configuration Hibernate pour MySQL -->
            <property name="hibernate.dialect" value="org.hibernate.dialect.MySQL8Dialect"/>
            <property name="hibernate.hbm2ddl.auto" value="update"/>
            <property name="hibernate.show_sql" value="true"/>
            <property name="hibernate.format_sql" value="true"/>
            <property name="hibernate.use_sql_comments" value="true"/>

            <!-- Configuration MySQL spécifique -->
            <property name="hibernate.connection.characterEncoding" value="utf8"/>
            <property name="hibernate.connection.useUnicode" value="true"/>
            <property name="hibernate.connection.autoReconnect" value="true"/>

            <!-- Configuration pour les connexions -->
            <property name="hibernate.connection.pool_size" value="10"/>
            <property name="hibernate.current_session_context_class" value="thread"/>
        </properties>
    </persistence-unit>
</persistence>
