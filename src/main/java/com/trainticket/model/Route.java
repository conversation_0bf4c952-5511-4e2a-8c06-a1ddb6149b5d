package com.trainticket.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "routes")
public class Route {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "La ville de départ est obligatoire")
    @Column(name = "departure_city", nullable = false)
    private String departureCity;
    
    @NotBlank(message = "La ville d'arrivée est obligatoire")
    @Column(name = "arrival_city", nullable = false)
    private String arrivalCity;
    
    @NotNull(message = "L'heure de départ est obligatoire")
    @Column(name = "departure_time", nullable = false)
    private LocalDateTime departureTime;
    
    @NotNull(message = "L'heure d'arrivée est obligatoire")
    @Column(name = "arrival_time", nullable = false)
    private LocalDateTime arrivalTime;
    
    @NotNull(message = "Le prix est obligatoire")
    @Positive(message = "Le prix doit être positif")
    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal price;
    
    @Column(name = "available_seats", nullable = false)
    private Integer availableSeats;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "train_id", nullable = false)
    private Train train;
    
    @OneToMany(mappedBy = "route", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Ticket> tickets = new ArrayList<>();
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    // Constructeurs
    public Route() {}
    
    public Route(String departureCity, String arrivalCity, LocalDateTime departureTime, 
                 LocalDateTime arrivalTime, BigDecimal price, Train train) {
        this.departureCity = departureCity;
        this.arrivalCity = arrivalCity;
        this.departureTime = departureTime;
        this.arrivalTime = arrivalTime;
        this.price = price;
        this.train = train;
        this.availableSeats = train != null ? train.getCapacity() : 0;
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getDepartureCity() {
        return departureCity;
    }
    
    public void setDepartureCity(String departureCity) {
        this.departureCity = departureCity;
    }
    
    public String getArrivalCity() {
        return arrivalCity;
    }
    
    public void setArrivalCity(String arrivalCity) {
        this.arrivalCity = arrivalCity;
    }
    
    public LocalDateTime getDepartureTime() {
        return departureTime;
    }
    
    public void setDepartureTime(LocalDateTime departureTime) {
        this.departureTime = departureTime;
    }
    
    public LocalDateTime getArrivalTime() {
        return arrivalTime;
    }
    
    public void setArrivalTime(LocalDateTime arrivalTime) {
        this.arrivalTime = arrivalTime;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public Integer getAvailableSeats() {
        return availableSeats;
    }
    
    public void setAvailableSeats(Integer availableSeats) {
        this.availableSeats = availableSeats;
    }
    
    public Train getTrain() {
        return train;
    }
    
    public void setTrain(Train train) {
        this.train = train;
        if (train != null && this.availableSeats == null) {
            this.availableSeats = train.getCapacity();
        }
    }
    
    public List<Ticket> getTickets() {
        return tickets;
    }
    
    public void setTickets(List<Ticket> tickets) {
        this.tickets = tickets;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    // Méthodes utilitaires
    public boolean hasAvailableSeats(int requestedSeats) {
        return availableSeats != null && availableSeats >= requestedSeats;
    }
    
    public void reserveSeats(int seats) {
        if (hasAvailableSeats(seats)) {
            this.availableSeats -= seats;
        } else {
            throw new IllegalStateException("Pas assez de places disponibles");
        }
    }
    
    public void releaseSeats(int seats) {
        this.availableSeats += seats;
        if (train != null && this.availableSeats > train.getCapacity()) {
            this.availableSeats = train.getCapacity();
        }
    }
    
    public String getRouteDescription() {
        return departureCity + " → " + arrivalCity;
    }

    /**
     * Calcule la durée du trajet en minutes
     */
    public long getDurationInMinutes() {
        if (departureTime == null || arrivalTime == null) {
            return 0;
        }
        return java.time.Duration.between(departureTime, arrivalTime).toMinutes();
    }

    /**
     * Retourne la durée formatée du trajet
     */
    public String getFormattedDuration() {
        long minutes = getDurationInMinutes();
        if (minutes == 0) {
            return "Non défini";
        }

        long hours = minutes / 60;
        long remainingMinutes = minutes % 60;

        if (hours > 0) {
            return String.format("%dh%02d", hours, remainingMinutes);
        } else {
            return String.format("%d min", remainingMinutes);
        }
    }

    /**
     * Vérifie si le trajet est dans le futur
     */
    public boolean isFuture() {
        return departureTime != null && departureTime.isAfter(LocalDateTime.now());
    }

    /**
     * Calcule le taux d'occupation du trajet
     */
    public double getOccupancyRate() {
        if (train == null || availableSeats == null) {
            return 0.0;
        }

        int totalCapacity = train.getCapacity();
        int occupiedSeats = totalCapacity - availableSeats;

        return (double) occupiedSeats / totalCapacity;
    }

    /**
     * Retourne le statut de disponibilité
     */
    public String getAvailabilityStatus() {
        if (availableSeats == null || train == null) {
            return "Non défini";
        }

        double occupancyRate = getOccupancyRate();

        if (availableSeats == 0) {
            return "Complet";
        } else if (occupancyRate >= 0.9) {
            return "Peu de places";
        } else if (occupancyRate >= 0.7) {
            return "Places limitées";
        } else {
            return "Disponible";
        }
    }
    
    @Override
    public String toString() {
        return "Route{" +
                "id=" + id +
                ", departureCity='" + departureCity + '\'' +
                ", arrivalCity='" + arrivalCity + '\'' +
                ", departureTime=" + departureTime +
                ", arrivalTime=" + arrivalTime +
                ", price=" + price +
                ", availableSeats=" + availableSeats +
                '}';
    }
}
