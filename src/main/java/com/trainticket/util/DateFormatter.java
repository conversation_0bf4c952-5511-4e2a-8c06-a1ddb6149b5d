package com.trainticket.util;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * Utilitaire pour le formatage des dates LocalDateTime et calcul de durées
 */
public class DateFormatter {
    
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private static final DateTimeFormatter DATETIME_FORMAT = DateTimeFormatter.ofPattern("dd/MM/yyyy à HH:mm");
    private static final DateTimeFormatter TIME_FORMAT = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter MONTH_YEAR_FORMAT = DateTimeFormatter.ofPattern("MMMM yyyy", Locale.FRENCH);
    private static final DateTimeFormatter FULL_DATE_FORMAT = DateTimeFormatter.ofPattern("EEEE dd MMMM yyyy", Locale.FRENCH);
    
    /**
     * Formate une date au format dd/MM/yyyy
     */
    public static String formatDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "Non défini";
        }
        return dateTime.format(DATE_FORMAT);
    }
    
    /**
     * Formate une date au format dd/MM/yyyy à HH:mm
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "Non défini";
        }
        return dateTime.format(DATETIME_FORMAT);
    }
    
    /**
     * Formate une heure au format HH:mm
     */
    public static String formatTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "Non défini";
        }
        return dateTime.format(TIME_FORMAT);
    }
    
    /**
     * Formate une date au format "mois année" en français
     */
    public static String formatMonthYear(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "Non défini";
        }
        return dateTime.format(MONTH_YEAR_FORMAT);
    }
    
    /**
     * Formate une date complète en français (ex: "lundi 28 mai 2025")
     */
    public static String formatFullDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "Non défini";
        }
        return dateTime.format(FULL_DATE_FORMAT);
    }
    
    /**
     * Retourne la date de dernière mise à jour ou la date de création si pas de mise à jour
     */
    public static String formatLastUpdate(LocalDateTime updatedAt, LocalDateTime createdAt) {
        LocalDateTime dateToFormat = updatedAt != null ? updatedAt : createdAt;
        return formatDateTime(dateToFormat);
    }
    
    /**
     * Calcule et formate la durée entre deux dates
     */
    public static String formatDuration(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return "Non défini";
        }
        
        Duration duration = Duration.between(start, end);
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;
        
        if (hours > 0) {
            return String.format("%dh%02d", hours, minutes);
        } else {
            return String.format("%d min", minutes);
        }
    }
    
    /**
     * Calcule la durée en minutes entre deux dates
     */
    public static long getDurationInMinutes(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return Duration.between(start, end).toMinutes();
    }
    
    /**
     * Vérifie si une date est dans le futur
     */
    public static boolean isFuture(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        return dateTime.isAfter(LocalDateTime.now());
    }
    
    /**
     * Vérifie si une date est dans le passé
     */
    public static boolean isPast(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        return dateTime.isBefore(LocalDateTime.now());
    }
}
