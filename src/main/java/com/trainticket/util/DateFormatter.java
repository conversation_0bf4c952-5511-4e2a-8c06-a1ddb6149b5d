package com.trainticket.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * Utilitaire pour le formatage des dates LocalDateTime
 */
public class DateFormatter {
    
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private static final DateTimeFormatter DATETIME_FORMAT = DateTimeFormatter.ofPattern("dd/MM/yyyy à HH:mm");
    private static final DateTimeFormatter MONTH_YEAR_FORMAT = DateTimeFormatter.ofPattern("MMMM yyyy", Locale.FRENCH);
    
    /**
     * Formate une date au format dd/MM/yyyy
     */
    public static String formatDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "Non défini";
        }
        return dateTime.format(DATE_FORMAT);
    }
    
    /**
     * Formate une date au format dd/MM/yyyy à HH:mm
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "Non défini";
        }
        return dateTime.format(DATETIME_FORMAT);
    }
    
    /**
     * Formate une date au format "mois année" en français
     */
    public static String formatMonthYear(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "Non défini";
        }
        return dateTime.format(MONTH_YEAR_FORMAT);
    }
    
    /**
     * Retourne la date de dernière mise à jour ou la date de création si pas de mise à jour
     */
    public static String formatLastUpdate(LocalDateTime updatedAt, LocalDateTime createdAt) {
        LocalDateTime dateToFormat = updatedAt != null ? updatedAt : createdAt;
        return formatDateTime(dateToFormat);
    }
}
