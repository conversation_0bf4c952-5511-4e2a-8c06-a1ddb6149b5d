package com.trainticket.util;

import com.trainticket.dao.impl.RouteDAOImpl;
import com.trainticket.model.Route;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.Persistence;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Utilitaire pour mettre à jour les dates des trajets existants
 */
public class UpdateRouteDates {
    
    public static void main(String[] args) {
        EntityManagerFactory emf = null;
        EntityManager em = null;
        
        try {
            System.out.println("🔄 Mise à jour des dates des trajets...");
            
            // Initialiser EntityManager
            emf = Persistence.createEntityManagerFactory("trainTicketPU");
            em = emf.createEntityManager();
            
            RouteDAOImpl routeDAO = new RouteDAOImpl();
            
            // Récupérer tous les trajets
            List<Route> routes = routeDAO.findAll();
            System.out.println("📊 Nombre de trajets trouvés : " + routes.size());
            
            em.getTransaction().begin();
            
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime baseDate = now.plusDays(1).withHour(6).withMinute(0).withSecond(0).withNano(0);
            
            int updatedCount = 0;
            
            for (Route route : routes) {
                // Calculer la nouvelle date de départ
                LocalDateTime originalDeparture = route.getDepartureTime();
                LocalDateTime originalArrival = route.getArrivalTime();
                
                // Calculer la durée du trajet
                long durationMinutes = java.time.Duration.between(originalDeparture, originalArrival).toMinutes();
                
                // Calculer le décalage par rapport à la date de base
                int routeIndex = routes.indexOf(route);
                int dayOffset = routeIndex / 16; // 16 trajets par jour (8 aller + 8 retour)
                int hourOffset = (routeIndex % 16) / 2; // 2 trajets par heure
                
                LocalDateTime newDeparture = baseDate
                    .plusDays(dayOffset)
                    .plusHours(hourOffset * 2);
                
                LocalDateTime newArrival = newDeparture.plusMinutes(durationMinutes);
                
                // Mettre à jour les dates
                route.setDepartureTime(newDeparture);
                route.setArrivalTime(newArrival);
                
                em.merge(route);
                updatedCount++;
                
                if (updatedCount % 50 == 0) {
                    System.out.println("✅ " + updatedCount + " trajets mis à jour...");
                }
            }
            
            em.getTransaction().commit();
            
            System.out.println("✅ Mise à jour terminée !");
            System.out.println("📊 Total des trajets mis à jour : " + updatedCount);
            System.out.println("📅 Nouvelle plage de dates : " + baseDate.toLocalDate() + " à " + 
                baseDate.plusDays(routes.size() / 16).toLocalDate());
            
        } catch (Exception e) {
            if (em != null && em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            System.err.println("❌ Erreur lors de la mise à jour : " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (em != null) {
                em.close();
            }
            if (emf != null) {
                emf.close();
            }
        }
    }
}
