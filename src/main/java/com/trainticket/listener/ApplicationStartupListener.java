package com.trainticket.listener;

import com.trainticket.service.DataInitializationService;
import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.ServletContextListener;
import jakarta.servlet.annotation.WebListener;

@WebListener
public class ApplicationStartupListener implements ServletContextListener {

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        System.out.println("🚀 Démarrage de l'application TrainTicket...");
        
        try {
            // Initialiser les données de test si nécessaire
            DataInitializationService dataInitService = new DataInitializationService();
            dataInitService.initializeAllData();
            dataInitService.printDataSummary();
            
            System.out.println("✅ Application TrainTicket démarrée avec succès !");
            
        } catch (Exception e) {
            System.err.println("❌ Erreur lors du démarrage de l'application : " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        System.out.println("🛑 Arrêt de l'application TrainTicket...");
    }
}
