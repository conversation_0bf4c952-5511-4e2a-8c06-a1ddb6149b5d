package com.trainticket.service;

import com.trainticket.dao.RouteDAO;
import com.trainticket.dao.impl.RouteDAOImpl;
import com.trainticket.model.Route;
import com.trainticket.model.Train;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class RouteService {

    private final RouteDAO routeDAO;

    public RouteService() {
        this.routeDAO = new RouteDAOImpl();
    }

    public RouteService(RouteDAO routeDAO) {
        this.routeDAO = routeDAO;
    }

    /**
     * Récupère le DAO pour accès direct
     */
    public RouteDAO getRouteDAO() {
        return routeDAO;
    }

    /**
     * Recherche des trajets par ville de départ et d'arrivée
     */
    public List<Route> searchRoutes(String departureCity, String arrivalCity) {
        if (departureCity == null || arrivalCity == null ||
            departureCity.trim().isEmpty() || arrivalCity.trim().isEmpty()) {
            throw new IllegalArgumentException("Les villes de départ et d'arrivée sont obligatoires");
        }

        return routeDAO.findByDepartureAndArrivalCities(
            departureCity.trim(), arrivalCity.trim())
            .stream()
            .filter(route -> route.getDepartureTime().isAfter(LocalDateTime.now()))
            .collect(Collectors.toList());
    }

    /**
     * Recherche des trajets par ville de départ, d'arrivée et plage de dates
     */
    public List<Route> searchRoutes(String departureCity, String arrivalCity,
                                  LocalDateTime startDateTime, LocalDateTime endDateTime) {
        if (departureCity == null || arrivalCity == null || startDateTime == null || endDateTime == null ||
            departureCity.trim().isEmpty() || arrivalCity.trim().isEmpty()) {
            throw new IllegalArgumentException("Les villes de départ, d'arrivée et les dates sont obligatoires");
        }

        return routeDAO.findByDepartureAndArrivalCitiesAndDate(
            departureCity.trim(), arrivalCity.trim(), startDateTime, endDateTime)
            .stream()
            .filter(route -> route.getDepartureTime().isAfter(LocalDateTime.now()))
            .collect(Collectors.toList());
    }

    /**
     * Recherche des trajets par ville de départ, d'arrivée et date
     */
    public List<Route> searchRoutes(String departureCity, String arrivalCity, LocalDate date) {
        if (departureCity == null || arrivalCity == null || date == null ||
            departureCity.trim().isEmpty() || arrivalCity.trim().isEmpty()) {
            throw new IllegalArgumentException("Les villes de départ, d'arrivée et la date sont obligatoires");
        }

        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX);

        return routeDAO.findByDepartureAndArrivalCitiesAndDate(
            departureCity.trim(), arrivalCity.trim(), startOfDay, endOfDay)
            .stream()
            .filter(route -> route.getDepartureTime().isAfter(LocalDateTime.now()))
            .collect(Collectors.toList());
    }

    /**
     * Recherche des trajets avec des places disponibles
     */
    public List<Route> searchAvailableRoutes(String departureCity, String arrivalCity,
                                           LocalDate date, int passengerCount) {

        List<Route> routes = searchRoutes(departureCity, arrivalCity, date);

        return routes.stream()
            .filter(route -> route.hasAvailableSeats(passengerCount))
            .collect(Collectors.toList());
    }

    /**
     * Trouve un trajet par son ID
     */
    public Optional<Route> findById(Long id) {
        return routeDAO.findById(id);
    }

    /**
     * Trouve tous les trajets
     */
    public List<Route> findAll() {
        return routeDAO.findAll();
    }

    /**
     * Trouve tous les trajets actifs
     */
    public List<Route> findActiveRoutes() {
        return routeDAO.findAll()
            .stream()
            .filter(route -> route.getIsActive() != null && route.getIsActive())
            .collect(Collectors.toList());
    }

    /**
     * Trouve tous les trajets futurs
     */
    public List<Route> findFutureRoutes() {
        return routeDAO.findFutureRoutes();
    }

    /**
     * Trouve tous les trajets d'un train
     */
    public List<Route> findRoutesByTrain(Train train) {
        return routeDAO.findByTrain(train);
    }

    /**
     * Trouve tous les trajets dans une plage de dates
     */
    public List<Route> findRoutesByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return routeDAO.findByDateRange(startDate, endDate);
    }

    /**
     * Trouve toutes les villes de départ disponibles
     */
    public List<String> getAllDepartureCities() {
        return routeDAO.findDistinctDepartureCities();
    }

    /**
     * Trouve toutes les villes d'arrivée disponibles
     */
    public List<String> getAllArrivalCities() {
        return routeDAO.findDistinctArrivalCities();
    }

    /**
     * Trouve les villes d'arrivée possibles depuis une ville de départ
     */
    public List<String> getArrivalCitiesFromDeparture(String departureCity) {
        return routeDAO.findByDepartureCity(departureCity)
            .stream()
            .map(Route::getArrivalCity)
            .distinct()
            .sorted()
            .collect(Collectors.toList());
    }

    /**
     * Vérifie la disponibilité d'un trajet pour un nombre de passagers
     */
    public boolean isRouteAvailable(Long routeId, int passengerCount) {
        Optional<Route> routeOpt = routeDAO.findById(routeId);

        if (routeOpt.isEmpty()) {
            return false;
        }

        Route route = routeOpt.get();

        // Vérifier que le trajet est actif et futur
        if (!route.getIsActive() || route.getDepartureTime().isBefore(LocalDateTime.now())) {
            return false;
        }

        // Vérifier la disponibilité des places
        return route.hasAvailableSeats(passengerCount);
    }

    /**
     * Réserve des places sur un trajet
     */
    public boolean reserveSeats(Long routeId, int passengerCount) {
        Optional<Route> routeOpt = routeDAO.findById(routeId);

        if (routeOpt.isEmpty()) {
            return false;
        }

        Route route = routeOpt.get();

        if (!isRouteAvailable(routeId, passengerCount)) {
            return false;
        }

        try {
            route.reserveSeats(passengerCount);
            routeDAO.update(route);
            return true;
        } catch (IllegalStateException e) {
            return false;
        }
    }

    /**
     * Libère des places sur un trajet (en cas d'annulation)
     */
    public void releaseSeats(Long routeId, int passengerCount) {
        Optional<Route> routeOpt = routeDAO.findById(routeId);

        if (routeOpt.isPresent()) {
            Route route = routeOpt.get();
            route.releaseSeats(passengerCount);
            routeDAO.update(route);
        }
    }

    /**
     * Trouve les trajets populaires (avec le moins de places disponibles)
     */
    public List<Route> findPopularRoutes(int limit) {
        return routeDAO.findFutureRoutes()
            .stream()
            .filter(route -> route.getAvailableSeats() != null && route.getTrain() != null)
            .sorted((r1, r2) -> {
                // Calculer le pourcentage d'occupation
                double occupancy1 = 1.0 - (double) r1.getAvailableSeats() / r1.getTrain().getCapacity();
                double occupancy2 = 1.0 - (double) r2.getAvailableSeats() / r2.getTrain().getCapacity();
                return Double.compare(occupancy2, occupancy1); // Ordre décroissant
            })
            .limit(limit)
            .collect(Collectors.toList());
    }

    /**
     * Calcule le taux d'occupation d'un trajet
     */
    public double getOccupancyRate(Route route) {
        if (route.getTrain() == null || route.getAvailableSeats() == null) {
            return 0.0;
        }

        int totalCapacity = route.getTrain().getCapacity();
        int occupiedSeats = totalCapacity - route.getAvailableSeats();

        return (double) occupiedSeats / totalCapacity;
    }

    /**
     * Compte le nombre total de trajets
     */
    public long getTotalRoutesCount() {
        return routeDAO.count();
    }

    /**
     * Compte le nombre de trajets actifs
     */
    public long getActiveRoutesCount() {
        return routeDAO.countActiveRoutes();
    }

    /**
     * Récupère les trajets populaires pour l'admin
     */
    public List<Route> getPopularRoutes(int limit) {
        return findPopularRoutes(limit);
    }

    /**
     * Trouve les trajets par ville de départ
     */
    public List<Route> findRoutesByDepartureCity(String departureCity) {
        if (departureCity == null || departureCity.trim().isEmpty()) {
            throw new IllegalArgumentException("La ville de départ est obligatoire");
        }

        return routeDAO.findByDepartureCity(departureCity.trim())
            .stream()
            .filter(route -> route.getDepartureTime().isAfter(LocalDateTime.now()))
            .collect(Collectors.toList());
    }

    /**
     * Trouve les trajets par ville d'arrivée
     */
    public List<Route> findRoutesByArrivalCity(String arrivalCity) {
        if (arrivalCity == null || arrivalCity.trim().isEmpty()) {
            throw new IllegalArgumentException("La ville d'arrivée est obligatoire");
        }

        return routeDAO.findByArrivalCity(arrivalCity.trim())
            .stream()
            .filter(route -> route.getDepartureTime().isAfter(LocalDateTime.now()))
            .collect(Collectors.toList());
    }
}
