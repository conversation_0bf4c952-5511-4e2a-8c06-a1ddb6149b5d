package com.trainticket.servlet;

import com.trainticket.model.User;
import com.trainticket.service.UserService;
import com.trainticket.util.SessionUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.Optional;

@WebServlet(name = "AuthServlet", urlPatterns = {"/login", "/logout", "/register"})
public class AuthServlet extends HttpServlet {

    private UserService userService;

    @Override
    public void init() throws ServletException {
        userService = new UserService();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String path = request.getServletPath();

        switch (path) {
            case "/login":
                showLoginPage(request, response);
                break;
            case "/register":
                showRegisterPage(request, response);
                break;
            case "/logout":
                handleLogout(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String path = request.getServletPath();

        switch (path) {
            case "/login":
                handleLogin(request, response);
                break;
            case "/register":
                handleRegister(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
        }
    }

    private void showLoginPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Si l'utilisateur est déjà connecté, rediriger vers le dashboard
        if (SessionUtil.isUserLoggedIn(request)) {
            response.sendRedirect(request.getContextPath() + "/user/dashboard");
            return;
        }

        request.getRequestDispatcher("/WEB-INF/views/auth/login.jsp").forward(request, response);
    }

    private void showRegisterPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Si l'utilisateur est déjà connecté, rediriger vers le dashboard
        if (SessionUtil.isUserLoggedIn(request)) {
            response.sendRedirect(request.getContextPath() + "/user/dashboard");
            return;
        }

        request.getRequestDispatcher("/WEB-INF/views/auth/register.jsp").forward(request, response);
    }

    private void handleLogin(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String usernameOrEmail = request.getParameter("usernameOrEmail");
        String password = request.getParameter("password");
        String rememberMe = request.getParameter("rememberMe");

        try {
            // Validation des paramètres côté serveur
            if (usernameOrEmail == null || usernameOrEmail.trim().isEmpty()) {
                request.setAttribute("error", "Veuillez saisir votre email ou nom d'utilisateur");
                request.setAttribute("usernameOrEmail", usernameOrEmail);
                request.getRequestDispatcher("/WEB-INF/views/auth/login.jsp").forward(request, response);
                return;
            }

            if (password == null || password.trim().isEmpty()) {
                request.setAttribute("error", "Veuillez saisir votre mot de passe");
                request.setAttribute("usernameOrEmail", usernameOrEmail);
                request.getRequestDispatcher("/WEB-INF/views/auth/login.jsp").forward(request, response);
                return;
            }

            // Tentative d'authentification
            Optional<User> userOpt;

            try {
                userOpt = userService.authenticate(usernameOrEmail.trim(), password);
            } catch (Exception dbException) {
                // Si la base de données n'est pas disponible, utiliser des comptes de test
                userOpt = authenticateTestUser(usernameOrEmail.trim(), password);
            }

            if (userOpt.isPresent()) {
                User user = userOpt.get();

                // Vérifier si le compte est actif
                if (!user.getIsActive()) {
                    request.setAttribute("error", "Votre compte est désactivé. Contactez l'administrateur.");
                    request.setAttribute("usernameOrEmail", usernameOrEmail);
                    request.getRequestDispatcher("/WEB-INF/views/auth/login.jsp").forward(request, response);
                    return;
                }

                // Créer la session utilisateur
                SessionUtil.setCurrentUser(request, user);

                // Gérer "Se souvenir de moi" si nécessaire
                if ("on".equals(rememberMe)) {
                    // Logique pour les cookies persistants (à implémenter si nécessaire)
                    // CookieUtil.setRememberMeCookie(response, user.getId());
                }

                // Message de succès dans la session
                SessionUtil.setFlashMessage(request, "success",
                    "Connexion réussie ! Bienvenue " + user.getFirstName());

                // Redirection intelligente basée sur le rôle
                String redirectUrl = determineRedirectUrl(user, request);
                response.sendRedirect(redirectUrl);
                return;

            } else {
                // Échec de l'authentification - Message générique pour la sécurité
                request.setAttribute("error", "Email/nom d'utilisateur ou mot de passe incorrect");
                request.setAttribute("usernameOrEmail", usernameOrEmail);
                request.getRequestDispatcher("/WEB-INF/views/auth/login.jsp").forward(request, response);
                return;
            }

        } catch (Exception e) {
            // Log de l'erreur pour le debugging
            System.err.println("Erreur lors de la connexion : " + e.getMessage());
            e.printStackTrace();

            request.setAttribute("error", "Erreur technique lors de la connexion. Veuillez réessayer.");
            request.setAttribute("usernameOrEmail", usernameOrEmail);
            request.getRequestDispatcher("/WEB-INF/views/auth/login.jsp").forward(request, response);
            return;
        }
    }

    private void handleRegister(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String username = request.getParameter("username");
        String email = request.getParameter("email");
        String password = request.getParameter("password");
        String confirmPassword = request.getParameter("confirmPassword");
        String firstName = request.getParameter("firstName");
        String lastName = request.getParameter("lastName");
        String phoneNumber = request.getParameter("phoneNumber");

        try {
            // Validation des mots de passe
            if (!password.equals(confirmPassword)) {
                throw new IllegalArgumentException("Les mots de passe ne correspondent pas");
            }

            // Créer l'utilisateur
            User user = userService.registerUser(username, email, password, firstName, lastName, phoneNumber);

            // Connecter automatiquement l'utilisateur
            SessionUtil.setCurrentUser(request, user);

            // Message de succès
            SessionUtil.setFlashMessage(request, "success",
                "Inscription réussie ! Bienvenue " + user.getFirstName());

            // Redirection intelligente basée sur le rôle
            String redirectUrl = determineRedirectUrl(user, request);
            response.sendRedirect(redirectUrl);

        } catch (Exception e) {
            // Conserver les données saisies
            request.setAttribute("error", e.getMessage());
            request.setAttribute("username", username);
            request.setAttribute("email", email);
            request.setAttribute("firstName", firstName);
            request.setAttribute("lastName", lastName);
            request.setAttribute("phoneNumber", phoneNumber);

            request.getRequestDispatcher("/WEB-INF/views/auth/register.jsp").forward(request, response);
        }
    }

    /**
     * Détermine l'URL de redirection basée sur le rôle de l'utilisateur
     */
    private String determineRedirectUrl(User user, HttpServletRequest request) {
        String contextPath = request.getContextPath();

        switch (user.getRole()) {
            case ADMIN:
                return contextPath + "/admin/dashboard";
            case EMPLOYEE:
                return contextPath + "/employee/dashboard";
            case CLIENT:
            default:
                return contextPath + "/search";
        }
    }

    private void handleLogout(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        // Invalider la session
        SessionUtil.invalidateSession(request);

        // Message de déconnexion
        SessionUtil.setFlashMessage(request, "info", "Vous avez été déconnecté avec succès");

        // Redirection vers la page d'accueil
        response.sendRedirect(request.getContextPath() + "/");
    }
}
