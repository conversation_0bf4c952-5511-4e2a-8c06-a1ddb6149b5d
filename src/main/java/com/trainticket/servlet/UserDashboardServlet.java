package com.trainticket.servlet;

import com.trainticket.model.Reservation;
import com.trainticket.model.User;
import com.trainticket.service.ReservationService;
import com.trainticket.service.UserService;
import com.trainticket.util.SessionUtil;
import com.trainticket.util.DateFormatter;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@WebServlet(name = "UserDashboardServlet", urlPatterns = {"/user/dashboard", "/user/reservations", "/user/profile", "/user/change-password"})
public class UserDashboardServlet extends HttpServlet {

    private ReservationService reservationService;
    private UserService userService;

    @Override
    public void init() throws ServletException {
        reservationService = new ReservationService();
        userService = new UserService();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        User currentUser = SessionUtil.getCurrentUser(request);
        if (currentUser == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        String path = request.getServletPath();

        switch (path) {
            case "/user/dashboard":
                showDashboard(request, response, currentUser);
                break;
            case "/user/reservations":
                showReservations(request, response, currentUser);
                break;
            case "/user/profile":
                showProfile(request, response, currentUser);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        User currentUser = SessionUtil.getCurrentUser(request);
        if (currentUser == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        String path = request.getServletPath();

        switch (path) {
            case "/user/profile":
                handleProfileUpdate(request, response, currentUser);
                break;
            case "/user/change-password":
                handlePasswordChange(request, response, currentUser);
                break;
            default:
                response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
        }
    }

    private void showDashboard(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {

        try {
            // Version simplifiée du dashboard
            request.setAttribute("currentUser", currentUser);

            // Initialiser avec des valeurs par défaut
            request.setAttribute("recentReservations", java.util.Collections.emptyList());
            request.setAttribute("activeReservations", java.util.Collections.emptyList());
            request.setAttribute("totalReservations", 0L);

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement du dashboard : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/user/dashboard.jsp").forward(request, response);
    }

    private void showReservations(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {

        try {
            // Charger toutes les réservations de l'utilisateur
            List<Reservation> allReservations = reservationService.findUserReservations(currentUser);

            // Filtrer par statut si demandé
            String statusFilter = request.getParameter("status");
            if (statusFilter != null && !statusFilter.trim().isEmpty()) {
                // Ici vous pourriez filtrer par statut
                request.setAttribute("statusFilter", statusFilter);
            }

            request.setAttribute("currentUser", currentUser);
            request.setAttribute("reservations", allReservations);

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des réservations : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/user/reservations.jsp").forward(request, response);
    }

    private void showProfile(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {

        // Passer l'utilisateur et les dates formatées
        request.setAttribute("currentUser", currentUser);

        // Formater les dates pour éviter les erreurs dans la JSP
        request.setAttribute("formattedCreatedAt", DateFormatter.formatDate(currentUser.getCreatedAt()));
        request.setAttribute("formattedCreatedAtMonthYear", DateFormatter.formatMonthYear(currentUser.getCreatedAt()));
        request.setAttribute("formattedLastUpdate", DateFormatter.formatLastUpdate(currentUser.getUpdatedAt(), currentUser.getCreatedAt()));

        request.getRequestDispatcher("/WEB-INF/views/user/profile.jsp").forward(request, response);
    }

    private void handleProfileUpdate(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {

        String firstName = request.getParameter("firstName");
        String lastName = request.getParameter("lastName");
        String email = request.getParameter("email");
        String phoneNumber = request.getParameter("phoneNumber");

        try {
            // Mettre à jour le profil
            User updatedUser = userService.updateUserProfile(currentUser.getId(), firstName, lastName, email, phoneNumber);

            // Mettre à jour la session
            SessionUtil.setCurrentUser(request, updatedUser);

            // Message de succès
            request.setAttribute("success", "Profil mis à jour avec succès !");
            request.setAttribute("currentUser", updatedUser);

            // Formater les dates pour l'utilisateur mis à jour
            request.setAttribute("formattedCreatedAt", DateFormatter.formatDate(updatedUser.getCreatedAt()));
            request.setAttribute("formattedCreatedAtMonthYear", DateFormatter.formatMonthYear(updatedUser.getCreatedAt()));
            request.setAttribute("formattedLastUpdate", DateFormatter.formatLastUpdate(updatedUser.getUpdatedAt(), updatedUser.getCreatedAt()));

        } catch (Exception e) {
            request.setAttribute("error", e.getMessage());
            request.setAttribute("currentUser", currentUser);

            // Formater les dates même en cas d'erreur
            request.setAttribute("formattedCreatedAt", DateFormatter.formatDate(currentUser.getCreatedAt()));
            request.setAttribute("formattedCreatedAtMonthYear", DateFormatter.formatMonthYear(currentUser.getCreatedAt()));
            request.setAttribute("formattedLastUpdate", DateFormatter.formatLastUpdate(currentUser.getUpdatedAt(), currentUser.getCreatedAt()));
        }

        request.getRequestDispatcher("/WEB-INF/views/user/profile.jsp").forward(request, response);
    }

    private void handlePasswordChange(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {

        String currentPassword = request.getParameter("currentPassword");
        String newPassword = request.getParameter("newPassword");
        String confirmNewPassword = request.getParameter("confirmNewPassword");

        try {
            // Validation côté serveur
            if (!newPassword.equals(confirmNewPassword)) {
                throw new IllegalArgumentException("Les nouveaux mots de passe ne correspondent pas");
            }

            // Changer le mot de passe
            boolean success = userService.changePassword(currentUser.getId(), currentPassword, newPassword);

            if (success) {
                SessionUtil.setFlashMessage(request, "success", "Mot de passe modifié avec succès !");
                response.sendRedirect(request.getContextPath() + "/user/profile");
            } else {
                request.setAttribute("error", "Mot de passe actuel incorrect");
                request.setAttribute("currentUser", currentUser);
                request.getRequestDispatcher("/WEB-INF/views/user/profile.jsp").forward(request, response);
            }

        } catch (Exception e) {
            request.setAttribute("error", e.getMessage());
            request.setAttribute("currentUser", currentUser);

            // Formater les dates même en cas d'erreur
            request.setAttribute("formattedCreatedAt", DateFormatter.formatDate(currentUser.getCreatedAt()));
            request.setAttribute("formattedCreatedAtMonthYear", DateFormatter.formatMonthYear(currentUser.getCreatedAt()));
            request.setAttribute("formattedLastUpdate", DateFormatter.formatLastUpdate(currentUser.getUpdatedAt(), currentUser.getCreatedAt()));

            request.getRequestDispatcher("/WEB-INF/views/user/profile.jsp").forward(request, response);
        }
    }
}
