package com.trainticket.servlet;

import com.trainticket.model.Route;
import com.trainticket.model.User;
import com.trainticket.service.RouteService;
import com.trainticket.util.SessionUtil;
import com.trainticket.util.DateFormatter;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@WebServlet(name = "SearchServlet", urlPatterns = {"/search", "/search-routes"})
public class SearchServlet extends HttpServlet {

    private RouteService routeService;

    @Override
    public void init() throws ServletException {
        routeService = new RouteService();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String path = request.getServletPath();

        if ("/search".equals(path)) {
            showSearchPage(request, response);
        } else if ("/search-routes".equals(path)) {
            handleSearch(request, response);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        handleSearch(request, response);
    }

    private void showSearchPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Récupérer l'utilisateur connecté
        User currentUser = SessionUtil.getCurrentUser(request);
        request.setAttribute("currentUser", currentUser);

        // Charger les villes disponibles pour les listes déroulantes
        try {
            List<String> departureCities = routeService.getAllDepartureCities();
            List<String> arrivalCities = routeService.getAllArrivalCities();

            request.setAttribute("departureCities", departureCities);
            request.setAttribute("arrivalCities", arrivalCities);

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des villes : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
    }

    private void handleSearch(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        System.out.println("=== NOUVELLE RECHERCHE SIMPLIFIÉE ===");

        String departureCity = request.getParameter("departureCity");
        String arrivalCity = request.getParameter("arrivalCity");
        String departureDateStr = request.getParameter("date");
        String departureTimeStr = request.getParameter("time");
        String passengerCountStr = request.getParameter("passengers");

        System.out.println("Paramètres reçus:");
        System.out.println("- Ville de départ: " + departureCity);
        System.out.println("- Ville d'arrivée: " + arrivalCity);
        System.out.println("- Date: " + departureDateStr);
        System.out.println("- Heure: " + departureTimeStr);
        System.out.println("- Passagers: " + passengerCountStr);

        List<Route> routes = new ArrayList<>();
        String errorMessage = null;

        try {
            // Récupérer TOUS les trajets de la base de données
            System.out.println("Récupération de tous les trajets...");
            routes = routeService.findAll();
            System.out.println("Nombre total de trajets trouvés: " + routes.size());

            // Afficher les 10 premiers trajets pour debug
            System.out.println("=== PREMIERS TRAJETS DANS LA BASE ===");
            for (int i = 0; i < Math.min(10, routes.size()); i++) {
                Route route = routes.get(i);
                System.out.println((i+1) + ". " + route.getDepartureCity() +
                                 " → " + route.getArrivalCity() +
                                 " le " + route.getDepartureTime().toLocalDate() +
                                 " à " + route.getDepartureTime().toLocalTime() +
                                 " (places: " + route.getAvailableSeats() + ")");
            }

            // Filtrer par ville de départ si spécifiée
            if (departureCity != null && !departureCity.trim().isEmpty()) {
                System.out.println("Filtrage par ville de départ: " + departureCity);
                routes = routes.stream()
                    .filter(route -> route.getDepartureCity().toLowerCase().contains(departureCity.toLowerCase().trim()))
                    .collect(Collectors.toList());
                System.out.println("Trajets après filtrage départ: " + routes.size());
            }

            // Filtrer par ville d'arrivée si spécifiée
            if (arrivalCity != null && !arrivalCity.trim().isEmpty()) {
                System.out.println("Filtrage par ville d'arrivée: " + arrivalCity);
                routes = routes.stream()
                    .filter(route -> route.getArrivalCity().toLowerCase().contains(arrivalCity.toLowerCase().trim()))
                    .collect(Collectors.toList());
                System.out.println("Trajets après filtrage arrivée: " + routes.size());
            }

            // Filtrer par date si spécifiée
            if (departureDateStr != null && !departureDateStr.trim().isEmpty()) {
                try {
                    LocalDate searchDate = LocalDate.parse(departureDateStr);
                    System.out.println("Filtrage par date: " + searchDate);
                    routes = routes.stream()
                        .filter(route -> route.getDepartureTime().toLocalDate().equals(searchDate))
                        .collect(Collectors.toList());
                    System.out.println("Trajets après filtrage date: " + routes.size());
                } catch (Exception e) {
                    System.out.println("Date invalide, ignorée: " + departureDateStr);
                }
            }

            // Filtrer par heure si spécifiée
            if (departureTimeStr != null && !departureTimeStr.trim().isEmpty()) {
                System.out.println("Filtrage par période: " + departureTimeStr);
                routes = filterByTimeRange(routes, departureTimeStr);
                System.out.println("Trajets après filtrage heure: " + routes.size());
            }

            // Filtrer par nombre de places disponibles
            if (passengerCountStr != null && !passengerCountStr.trim().isEmpty()) {
                try {
                    int passengers = Integer.parseInt(passengerCountStr);
                    System.out.println("Filtrage par nombre de passagers: " + passengers);
                    routes = routes.stream()
                        .filter(route -> route.getAvailableSeats() >= passengers)
                        .collect(Collectors.toList());
                    System.out.println("Trajets après filtrage passagers: " + routes.size());
                } catch (NumberFormatException e) {
                    System.out.println("Nombre de passagers invalide, ignoré: " + passengerCountStr);
                }
            }

            System.out.println("=== RÉSULTATS FINAUX ===");
            for (int i = 0; i < Math.min(5, routes.size()); i++) {
                Route route = routes.get(i);
                System.out.println((i+1) + ". " + route.getDepartureCity() +
                                 " → " + route.getArrivalCity() +
                                 " le " + route.getDepartureTime().toLocalDate() +
                                 " à " + route.getDepartureTime().toLocalTime() +
                                 " (places: " + route.getAvailableSeats() + ")");
            }

        } catch (Exception e) {
            System.err.println("ERREUR lors de la recherche: " + e.getMessage());
            e.printStackTrace();
            errorMessage = "Erreur lors de la recherche: " + e.getMessage();
        }

        // Préparer les attributs pour la JSP
        request.setAttribute("routes", routes);
        request.setAttribute("departureCity", departureCity);
        request.setAttribute("arrivalCity", arrivalCity);
        request.setAttribute("departureDate", departureDateStr);
        request.setAttribute("departureTime", departureTimeStr);
        request.setAttribute("passengerCount", passengerCountStr);
        request.setAttribute("searchPerformed", true);

        if (errorMessage != null) {
            request.setAttribute("error", errorMessage);
        } else if (routes.isEmpty()) {
            request.setAttribute("noResultsMessage", "Aucun trajet trouvé avec ces critères");
        } else {
            request.setAttribute("resultsMessage", routes.size() + " trajet(s) trouvé(s)");
            addSearchStatistics(request, routes);
        }

        System.out.println("Envoi de " + routes.size() + " trajets vers la JSP");

            // Préparer les données pour l'affichage
            request.setAttribute("routes", routes);
            request.setAttribute("departureCity", departureCity);
            request.setAttribute("arrivalCity", arrivalCity);
            request.setAttribute("passengerCount", passengerCount);
            request.setAttribute("searchPerformed", true);

            if (routes.isEmpty()) {
                request.setAttribute("noResultsMessage",
                    "Aucun trajet trouvé pour " + departureCity + " → " + arrivalCity +
                    " avec " + passengerCount + " passager(s)");
            } else {
                request.setAttribute("resultsMessage",
                    routes.size() + " trajet(s) trouvé(s) pour " + departureCity + " → " + arrivalCity);

                // Ajouter des statistiques sur les résultats
                addSearchStatistics(request, routes);
            }

        } catch (Exception e) {
            request.setAttribute("error", e.getMessage());
            request.setAttribute("departureCity", departureCity);
            request.setAttribute("arrivalCity", arrivalCity);
            request.setAttribute("departureDate", departureDateStr);
            request.setAttribute("departureTime", departureTimeStr);
            request.setAttribute("passengerCount", passengerCountStr);
        }

        // Recharger les villes pour les listes déroulantes
        try {
            List<String> departureCities = routeService.getAllDepartureCities();
            List<String> arrivalCities = routeService.getAllArrivalCities();

            request.setAttribute("departureCities", departureCities);
            request.setAttribute("arrivalCities", arrivalCities);

        } catch (Exception e) {
            // Log l'erreur mais ne pas interrompre l'affichage
            System.err.println("Erreur lors du chargement des villes : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
    }

    /**
     * Ajoute des statistiques sur les résultats de recherche
     */
    private void addSearchStatistics(HttpServletRequest request, List<Route> routes) {
        if (routes.isEmpty()) {
            return;
        }

        // Prix minimum et maximum
        double minPrice = routes.stream()
            .mapToDouble(route -> route.getPrice().doubleValue())
            .min()
            .orElse(0.0);

        double maxPrice = routes.stream()
            .mapToDouble(route -> route.getPrice().doubleValue())
            .max()
            .orElse(0.0);

        // Durée minimum et maximum
        long minDuration = routes.stream()
            .mapToLong(Route::getDurationInMinutes)
            .min()
            .orElse(0);

        long maxDuration = routes.stream()
            .mapToLong(Route::getDurationInMinutes)
            .max()
            .orElse(0);

        // Nombre total de places disponibles
        int totalAvailableSeats = routes.stream()
            .mapToInt(route -> route.getAvailableSeats() != null ? route.getAvailableSeats() : 0)
            .sum();

        // Types de trains disponibles
        long trainTypesCount = routes.stream()
            .map(route -> route.getTrain().getTrainType())
            .distinct()
            .count();

        request.setAttribute("minPrice", minPrice);
        request.setAttribute("maxPrice", maxPrice);
        request.setAttribute("minDuration",
            String.format("%dh%02d", minDuration / 60, minDuration % 60));
        request.setAttribute("maxDuration",
            String.format("%dh%02d", maxDuration / 60, maxDuration % 60));
        request.setAttribute("totalAvailableSeats", totalAvailableSeats);
        request.setAttribute("trainTypesCount", trainTypesCount);
    }

    /**
     * Filtre les trajets par plage horaire
     */
    private List<Route> filterByTimeRange(List<Route> routes, String timeRange) {
        if (timeRange == null || timeRange.trim().isEmpty()) {
            return routes;
        }

        String[] timeParts = timeRange.split("-");
        if (timeParts.length != 2) {
            return routes;
        }

        try {
            LocalTime startTime = LocalTime.parse(timeParts[0]);
            LocalTime endTime = LocalTime.parse(timeParts[1]);

            return routes.stream()
                .filter(route -> {
                    if (route.getDepartureTime() == null) {
                        return false;
                    }
                    LocalTime departureTime = route.getDepartureTime().toLocalTime();
                    return !departureTime.isBefore(startTime) && !departureTime.isAfter(endTime);
                })
                .collect(Collectors.toList());

        } catch (Exception e) {
            // En cas d'erreur de parsing, retourner tous les trajets
            return routes;
        }
    }
}
