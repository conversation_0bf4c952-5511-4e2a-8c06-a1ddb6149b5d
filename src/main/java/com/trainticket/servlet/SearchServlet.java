package com.trainticket.servlet;

import com.trainticket.model.Route;
import com.trainticket.model.User;
import com.trainticket.service.RouteService;
import com.trainticket.util.SessionUtil;
import com.trainticket.util.DateFormatter;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.stream.Collectors;

@WebServlet(name = "SearchServlet", urlPatterns = {"/search", "/search-routes"})
public class SearchServlet extends HttpServlet {

    private RouteService routeService;

    @Override
    public void init() throws ServletException {
        routeService = new RouteService();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String path = request.getServletPath();

        if ("/search".equals(path)) {
            showSearchPage(request, response);
        } else if ("/search-routes".equals(path)) {
            handleSearch(request, response);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        handleSearch(request, response);
    }

    private void showSearchPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Récupérer l'utilisateur connecté
        User currentUser = SessionUtil.getCurrentUser(request);
        request.setAttribute("currentUser", currentUser);

        // Charger les villes disponibles pour les listes déroulantes
        try {
            List<String> departureCities = routeService.getAllDepartureCities();
            List<String> arrivalCities = routeService.getAllArrivalCities();

            request.setAttribute("departureCities", departureCities);
            request.setAttribute("arrivalCities", arrivalCities);

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des villes : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
    }

    private void handleSearch(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String departureCity = request.getParameter("departureCity");
        String arrivalCity = request.getParameter("arrivalCity");
        String departureDateStr = request.getParameter("departureDate");
        String departureTimeStr = request.getParameter("departureTime");
        String passengerCountStr = request.getParameter("passengerCount");

        try {


            // Validation des paramètres
            if (departureCity == null || departureCity.trim().isEmpty()) {
                throw new IllegalArgumentException("La ville de départ est obligatoire");
            }

            if (arrivalCity == null || arrivalCity.trim().isEmpty()) {
                throw new IllegalArgumentException("La ville d'arrivée est obligatoire");
            }

            if (departureCity.trim().equalsIgnoreCase(arrivalCity.trim())) {
                throw new IllegalArgumentException("La ville de départ et d'arrivée ne peuvent pas être identiques");
            }

            // Nombre de passagers (par défaut 1)
            int passengerCount = 1;
            if (passengerCountStr != null && !passengerCountStr.trim().isEmpty()) {
                try {
                    passengerCount = Integer.parseInt(passengerCountStr);
                    if (passengerCount < 1 || passengerCount > 10) {
                        throw new IllegalArgumentException("Le nombre de passagers doit être entre 1 et 10");
                    }
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Nombre de passagers invalide");
                }
            }

            List<Route> routes;

            // Recherche avec ou sans date
            if (departureDateStr != null && !departureDateStr.trim().isEmpty()) {
                try {
                    LocalDate departureDate = LocalDate.parse(departureDateStr, DateTimeFormatter.ISO_LOCAL_DATE);

                    // Vérifier que la date n'est pas dans le passé
                    if (departureDate.isBefore(LocalDate.now())) {
                        throw new IllegalArgumentException("La date de départ ne peut pas être dans le passé");
                    }

                    routes = routeService.searchAvailableRoutes(departureCity, arrivalCity, departureDate, passengerCount);
                    System.out.println("Trajets trouvés avec date: " + routes.size());

                    // Filtrer par heure si spécifiée
                    if (departureTimeStr != null && !departureTimeStr.trim().isEmpty()) {
                        routes = filterByTimeRange(routes, departureTimeStr);
                        System.out.println("Trajets après filtrage par heure: " + routes.size());
                    }

                    request.setAttribute("departureDate", departureDateStr);
                    request.setAttribute("departureTime", departureTimeStr);

                } catch (DateTimeParseException e) {
                    throw new IllegalArgumentException("Format de date invalide");
                }
            } else {
                final int finalPassengerCount = passengerCount;
                List<Route> allRoutes = routeService.searchRoutes(departureCity, arrivalCity);
                System.out.println("Trajets trouvés sans date: " + allRoutes.size());

                routes = allRoutes.stream()
                    .filter(route -> route.hasAvailableSeats(finalPassengerCount))
                    .collect(Collectors.toList());
                System.out.println("Trajets avec places disponibles: " + routes.size());
            }

            // Préparer les données pour l'affichage
            request.setAttribute("routes", routes);
            request.setAttribute("departureCity", departureCity);
            request.setAttribute("arrivalCity", arrivalCity);
            request.setAttribute("passengerCount", passengerCount);
            request.setAttribute("searchPerformed", true);

            if (routes.isEmpty()) {
                request.setAttribute("noResultsMessage",
                    "Aucun trajet trouvé pour " + departureCity + " → " + arrivalCity +
                    " avec " + passengerCount + " passager(s)");
            } else {
                request.setAttribute("resultsMessage",
                    routes.size() + " trajet(s) trouvé(s) pour " + departureCity + " → " + arrivalCity);

                // Ajouter des statistiques sur les résultats
                addSearchStatistics(request, routes);
            }

        } catch (Exception e) {
            request.setAttribute("error", e.getMessage());
            request.setAttribute("departureCity", departureCity);
            request.setAttribute("arrivalCity", arrivalCity);
            request.setAttribute("departureDate", departureDateStr);
            request.setAttribute("departureTime", departureTimeStr);
            request.setAttribute("passengerCount", passengerCountStr);
        }

        // Recharger les villes pour les listes déroulantes
        try {
            List<String> departureCities = routeService.getAllDepartureCities();
            List<String> arrivalCities = routeService.getAllArrivalCities();

            request.setAttribute("departureCities", departureCities);
            request.setAttribute("arrivalCities", arrivalCities);

        } catch (Exception e) {
            // Log l'erreur mais ne pas interrompre l'affichage
            System.err.println("Erreur lors du chargement des villes : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
    }

    /**
     * Ajoute des statistiques sur les résultats de recherche
     */
    private void addSearchStatistics(HttpServletRequest request, List<Route> routes) {
        if (routes.isEmpty()) {
            return;
        }

        // Prix minimum et maximum
        double minPrice = routes.stream()
            .mapToDouble(route -> route.getPrice().doubleValue())
            .min()
            .orElse(0.0);

        double maxPrice = routes.stream()
            .mapToDouble(route -> route.getPrice().doubleValue())
            .max()
            .orElse(0.0);

        // Durée minimum et maximum
        long minDuration = routes.stream()
            .mapToLong(Route::getDurationInMinutes)
            .min()
            .orElse(0);

        long maxDuration = routes.stream()
            .mapToLong(Route::getDurationInMinutes)
            .max()
            .orElse(0);

        // Nombre total de places disponibles
        int totalAvailableSeats = routes.stream()
            .mapToInt(route -> route.getAvailableSeats() != null ? route.getAvailableSeats() : 0)
            .sum();

        // Types de trains disponibles
        long trainTypesCount = routes.stream()
            .map(route -> route.getTrain().getTrainType())
            .distinct()
            .count();

        request.setAttribute("minPrice", minPrice);
        request.setAttribute("maxPrice", maxPrice);
        request.setAttribute("minDuration",
            String.format("%dh%02d", minDuration / 60, minDuration % 60));
        request.setAttribute("maxDuration",
            String.format("%dh%02d", maxDuration / 60, maxDuration % 60));
        request.setAttribute("totalAvailableSeats", totalAvailableSeats);
        request.setAttribute("trainTypesCount", trainTypesCount);
    }

    /**
     * Filtre les trajets par plage horaire
     */
    private List<Route> filterByTimeRange(List<Route> routes, String timeRange) {
        if (timeRange == null || timeRange.trim().isEmpty()) {
            return routes;
        }

        String[] timeParts = timeRange.split("-");
        if (timeParts.length != 2) {
            return routes;
        }

        try {
            LocalTime startTime = LocalTime.parse(timeParts[0]);
            LocalTime endTime = LocalTime.parse(timeParts[1]);

            return routes.stream()
                .filter(route -> {
                    if (route.getDepartureTime() == null) {
                        return false;
                    }
                    LocalTime departureTime = route.getDepartureTime().toLocalTime();
                    return !departureTime.isBefore(startTime) && departureTime.isBefore(endTime);
                })
                .collect(Collectors.toList());

        } catch (Exception e) {
            // En cas d'erreur de parsing, retourner tous les trajets
            return routes;
        }
    }
}
