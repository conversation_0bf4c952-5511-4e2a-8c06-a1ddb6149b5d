package com.trainticket.servlet;

import com.trainticket.model.Route;
import com.trainticket.model.User;
import com.trainticket.service.RouteService;
import com.trainticket.util.SessionUtil;
import com.trainticket.util.DateFormatter;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.stream.Collectors;

@WebServlet(name = "SearchServlet", urlPatterns = {"/search", "/search-routes"})
public class SearchServlet extends HttpServlet {

    private RouteService routeService;

    @Override
    public void init() throws ServletException {
        routeService = new RouteService();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String path = request.getServletPath();

        if ("/search".equals(path)) {
            showSearchPage(request, response);
        } else if ("/search-routes".equals(path)) {
            handleSearch(request, response);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        handleSearch(request, response);
    }

    private void showSearchPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Récupérer l'utilisateur connecté
        User currentUser = SessionUtil.getCurrentUser(request);
        request.setAttribute("currentUser", currentUser);

        // Charger les villes disponibles pour les listes déroulantes
        try {
            List<String> departureCities = routeService.getAllDepartureCities();
            List<String> arrivalCities = routeService.getAllArrivalCities();

            request.setAttribute("departureCities", departureCities);
            request.setAttribute("arrivalCities", arrivalCities);

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des villes : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
    }

    private void handleSearch(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String departureCity = request.getParameter("departureCity");
        String arrivalCity = request.getParameter("arrivalCity");
        String departureDateStr = request.getParameter("departureDate");
        String passengerCountStr = request.getParameter("passengerCount");

        try {
            // Validation des paramètres
            if (departureCity == null || departureCity.trim().isEmpty()) {
                throw new IllegalArgumentException("La ville de départ est obligatoire");
            }

            if (arrivalCity == null || arrivalCity.trim().isEmpty()) {
                throw new IllegalArgumentException("La ville d'arrivée est obligatoire");
            }

            if (departureCity.trim().equalsIgnoreCase(arrivalCity.trim())) {
                throw new IllegalArgumentException("La ville de départ et d'arrivée ne peuvent pas être identiques");
            }

            // Nombre de passagers (par défaut 1)
            int passengerCount = 1;
            if (passengerCountStr != null && !passengerCountStr.trim().isEmpty()) {
                try {
                    passengerCount = Integer.parseInt(passengerCountStr);
                    if (passengerCount < 1 || passengerCount > 10) {
                        throw new IllegalArgumentException("Le nombre de passagers doit être entre 1 et 10");
                    }
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Nombre de passagers invalide");
                }
            }

            List<Route> routes;

            // Recherche avec ou sans date
            if (departureDateStr != null && !departureDateStr.trim().isEmpty()) {
                try {
                    LocalDate departureDate = LocalDate.parse(departureDateStr, DateTimeFormatter.ISO_LOCAL_DATE);

                    // Vérifier que la date n'est pas dans le passé
                    if (departureDate.isBefore(LocalDate.now())) {
                        throw new IllegalArgumentException("La date de départ ne peut pas être dans le passé");
                    }

                    routes = routeService.searchAvailableRoutes(departureCity, arrivalCity, departureDate, passengerCount);
                    request.setAttribute("departureDate", departureDateStr);

                } catch (DateTimeParseException e) {
                    throw new IllegalArgumentException("Format de date invalide");
                }
            } else {
                final int finalPassengerCount = passengerCount;
                routes = routeService.searchRoutes(departureCity, arrivalCity)
                    .stream()
                    .filter(route -> route.hasAvailableSeats(finalPassengerCount))
                    .collect(Collectors.toList());
            }

            // Préparer les données pour l'affichage
            request.setAttribute("routes", routes);
            request.setAttribute("departureCity", departureCity);
            request.setAttribute("arrivalCity", arrivalCity);
            request.setAttribute("passengerCount", passengerCount);
            request.setAttribute("searchPerformed", true);

            if (routes.isEmpty()) {
                request.setAttribute("noResultsMessage",
                    "Aucun trajet trouvé pour " + departureCity + " → " + arrivalCity +
                    " avec " + passengerCount + " passager(s)");
            } else {
                request.setAttribute("resultsMessage",
                    routes.size() + " trajet(s) trouvé(s) pour " + departureCity + " → " + arrivalCity);
            }

        } catch (Exception e) {
            request.setAttribute("error", e.getMessage());
            request.setAttribute("departureCity", departureCity);
            request.setAttribute("arrivalCity", arrivalCity);
            request.setAttribute("departureDate", departureDateStr);
            request.setAttribute("passengerCount", passengerCountStr);
        }

        // Recharger les villes pour les listes déroulantes
        try {
            List<String> departureCities = routeService.getAllDepartureCities();
            List<String> arrivalCities = routeService.getAllArrivalCities();

            request.setAttribute("departureCities", departureCities);
            request.setAttribute("arrivalCities", arrivalCities);

        } catch (Exception e) {
            // Log l'erreur mais ne pas interrompre l'affichage
            System.err.println("Erreur lors du chargement des villes : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
    }
}
