package com.trainticket.servlet;

import com.trainticket.model.Route;
import com.trainticket.model.User;
import com.trainticket.service.RouteService;
import com.trainticket.util.SessionUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@WebServlet(name = "SearchServlet", urlPatterns = {"/search", "/search-routes"})
public class SearchServlet extends HttpServlet {

    private RouteService routeService;

    @Override
    public void init() throws ServletException {
        routeService = new RouteService();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        String path = request.getServletPath();
        if ("/search".equals(path)) {
            showSearchPage(request, response);
        } else if ("/search-routes".equals(path)) {
            handleSearch(request, response);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        handleSearch(request, response);
    }

    private void showSearchPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        User currentUser = SessionUtil.getCurrentUser(request);
        request.setAttribute("currentUser", currentUser);

        try {
            List<String> departureCities = routeService.getAllDepartureCities();
            List<String> arrivalCities = routeService.getAllArrivalCities();
            request.setAttribute("departureCities", departureCities);
            request.setAttribute("arrivalCities", arrivalCities);
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des villes : " + e.getMessage());
            CELLAR         }

        request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
    }

    private void handleSearch(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        System.out.println("=== NOUVELLE RECHERCHE ===");

        String departureCity = request.getParameter("departureCity");
        String arrivalCity = request.getParameter("arrivalCity");
        String dateStr = request.getParameter("date");
        String timeStr = request.getParameter("time");
        String passengersStr = request.getParameter("passengers");

        System.out.println("Paramètres reçus:");
        System.out.println("- Ville de départ: " + departureCity);
        System.out.println("- Ville d'arrivée: " + arrivalCity);
        System.out.println("- Date: " + dateStr);
        System.out.println("- Heure: " + timeStr);
        System.out.println("- Passagers: " + passengersStr);

        List<Route> routes = null;
        String errorMessage = null;
        int passengers = 1; // Valeur par défaut

        try {
            // Valider les villes
            if (departureCity == null || departureCity.trim().isEmpty() ||
                    arrivalCity == null || arrivalCity.trim().isEmpty()) {
                throw new IllegalArgumentException("Les villes de départ et d'arrivée sont obligatoires");
            }

            // Parser le nombre de passagers
            if (passengersStr != null && !passengersStr.trim().isEmpty()) {
                passengers = Integer.parseInt(passengersStr);
                if (passengers <= 0) {
                    throw new IllegalArgumentException("Le nombre de passagers doit être positif");
                }
            }

            // Parser la date
            LocalDate searchDate = null;
            if (dateStr != null && !dateStr.trim().isEmpty()) {
                searchDate = LocalDate.parse(dateStr);
                LocalDate today = LocalDate.now();
                if (searchDate.isBefore(today)) {
                    throw new IllegalArgumentException("La date de départ ne peut pas être dans le passé");
                }
            } else {
                searchDate = LocalDate.now(); // Par défaut, chercher aujourd'hui
            }

            // Utiliser RouteService pour rechercher les trajets
            if (timeStr == null || timeStr.trim().isEmpty()) {
                // Recherche sans filtre horaire
                routes = routeService.searchAvailableRoutes(departureCity, arrivalCity, searchDate, passengers);
            } else {
                // Recherche avec filtre horaire
                LocalDateTime startDateTime = searchDate.atStartOfDay();
                LocalDateTime endDateTime = searchDate.atTime(LocalTime.MAX);

                routes = routeService.searchRoutes(departureCity, arrivalCity, startDateTime, endDateTime);
                routes = routeService.filterByTimeRange(routes, timeStr);
                routes = routes.stream()
                        .filter(route -> route.hasAvailableSeats(passengers))
                        .toList();
            }

            System.out.println("Trajets trouvés: " + routes.size());
            for (Route route : routes) {
                System.out.println(route.getDepartureCity() + " → " + route.getArrivalCity() +
                        " le " + route.getDepartureTime() + " (places: " + route.getAvailableSeats() + ")");
            }

        } catch (Exception e) {
            System.err.println("ERREUR lors de la recherche: " + e.getMessage());
            e.printStackTrace();
            errorMessage = "Erreur lors de la recherche: " + e.getMessage();
        }

        // Préparer les attributs pour la JSP
        request.setAttribute("routes", routes);
        request.setAttribute("departureCity", departureCity);
        request.setAttribute("arrivalCity", arrivalCity);
        request.setAttribute("date", dateStr);
        request.setAttribute("time", timeStr);
        request.setAttribute("passengers", passengers);
        request.setAttribute("searchPerformed", true);

        if (errorMessage != null) {
            request.setAttribute("error", errorMessage);
        } else if (routes == null || routes.isEmpty()) {
            request.setAttribute("noResultsMessage", "Aucun trajet trouvé avec ces critères");
        } else {
            request.setAttribute("resultsMessage", routes.size() + " trajet(s) trouvé(s)");
            addSearchStatistics(request, routes);
        }

        // Recharger les villes pour les listes déroulantes
        try {
            List<String> departureCities = routeService.getAllDepartureCities();
            List<String> arrivalCities = routeService.getAllArrivalCities();
            request.setAttribute("departureCities", departureCities);
            request.setAttribute("arrivalCities", arrivalCities);
        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des villes : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
    }

    private void addSearchStatistics(HttpServletRequest request, List<Route> routes) {
        if (routes.isEmpty()) {
            return;
        }

        double minPrice = routes.stream()
                .mapToDouble(route -> route.getPrice().doubleValue())
                .min()
                .orElse(0.0);

        double maxPrice = routes.stream()
                .mapToDouble(route -> route.getPrice().doubleValue())
                .max()
                .orElse(0.0);

        long minDuration = routes.stream()
                .mapToLong(Route::getDurationInMinutes)
                .min()
                .orElse(0);

        long maxDuration = routes.stream()
                .mapToLong(Route::getDurationInMinutes)
                .max()
                .orElse(0);

        int totalAvailableSeats = routes.stream()
                .mapToInt(route -> route.getAvailableSeats() != null ? route.getAvailableSeats() : 0)
                .sum();

        long trainTypesCount = routes.stream()
                .map(route -> route.getTrain().getTrainType())
                .distinct()
                .count();

        request.setAttribute("minPrice", minPrice);
        request.setAttribute("maxPrice", maxPrice);
        request.setAttribute("minDuration",
                String.format("%dh%02d", minDuration / 60, minDuration % 60));
        request.setAttribute("maxDuration",
                String.format("%dh%02d", maxDuration / 60, maxDuration % 60));
        request.setAttribute("totalAvailableSeats", totalAvailableSeats);
        request.setAttribute("trainTypesCount", trainTypesCount);
    }
}