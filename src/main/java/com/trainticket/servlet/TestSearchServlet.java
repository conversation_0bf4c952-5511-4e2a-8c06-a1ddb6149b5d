package com.trainticket.servlet;

import com.trainticket.model.Route;
import com.trainticket.service.RouteService;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

@WebServlet("/test-search")
public class TestSearchServlet extends HttpServlet {
    
    private RouteService routeService = new RouteService();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        
        try {
            // Test 1: Récupérer toutes les villes
            List<String> departureCities = routeService.getAllDepartureCities();
            List<String> arrivalCities = routeService.getAllArrivalCities();
            
            response.getWriter().println("<!DOCTYPE html>");
            response.getWriter().println("<html>");
            response.getWriter().println("<head>");
            response.getWriter().println("<title>Test de recherche</title>");
            response.getWriter().println("<meta charset='UTF-8'>");
            response.getWriter().println("<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>");
            response.getWriter().println("</head>");
            response.getWriter().println("<body>");
            response.getWriter().println("<div class='container mt-5'>");
            
            response.getWriter().println("<h2>🔍 Test de la fonction de recherche</h2>");
            
            // Afficher les villes disponibles
            response.getWriter().println("<div class='card mb-3'>");
            response.getWriter().println("<div class='card-header'><h5>Villes de départ (" + departureCities.size() + ")</h5></div>");
            response.getWriter().println("<div class='card-body'>");
            for (String city : departureCities) {
                response.getWriter().println("<span class='badge bg-primary me-2'>" + city + "</span>");
            }
            response.getWriter().println("</div></div>");
            
            response.getWriter().println("<div class='card mb-3'>");
            response.getWriter().println("<div class='card-header'><h5>Villes d'arrivée (" + arrivalCities.size() + ")</h5></div>");
            response.getWriter().println("<div class='card-body'>");
            for (String city : arrivalCities) {
                response.getWriter().println("<span class='badge bg-success me-2'>" + city + "</span>");
            }
            response.getWriter().println("</div></div>");
            
            // Test 2: Recherche Paris → Lyon
            if (departureCities.contains("Paris") && arrivalCities.contains("Lyon")) {
                response.getWriter().println("<div class='card mb-3'>");
                response.getWriter().println("<div class='card-header'><h5>Test: Paris → Lyon (demain)</h5></div>");
                response.getWriter().println("<div class='card-body'>");
                
                LocalDate tomorrow = LocalDate.now().plusDays(1);
                List<Route> routes = routeService.searchAvailableRoutes("Paris", "Lyon", tomorrow, 1);
                
                response.getWriter().println("<p><strong>Résultats:</strong> " + routes.size() + " trajet(s) trouvé(s)</p>");
                
                for (Route route : routes) {
                    response.getWriter().println("<div class='alert alert-info'>");
                    response.getWriter().println("<strong>" + route.getTrain().getName() + "</strong><br>");
                    response.getWriter().println("Départ: " + route.getDepartureTime() + "<br>");
                    response.getWriter().println("Arrivée: " + route.getArrivalTime() + "<br>");
                    response.getWriter().println("Prix: " + route.getPrice() + "€<br>");
                    response.getWriter().println("Places disponibles: " + route.getAvailableSeats());
                    response.getWriter().println("</div>");
                }
                
                response.getWriter().println("</div></div>");
            }
            
            // Test 3: Recherche sans date
            response.getWriter().println("<div class='card mb-3'>");
            response.getWriter().println("<div class='card-header'><h5>Test: Paris → Lyon (sans date)</h5></div>");
            response.getWriter().println("<div class='card-body'>");
            
            List<Route> routesNoDate = routeService.searchRoutes("Paris", "Lyon");
            response.getWriter().println("<p><strong>Résultats:</strong> " + routesNoDate.size() + " trajet(s) trouvé(s)</p>");
            
            int count = 0;
            for (Route route : routesNoDate) {
                if (count >= 5) break; // Limiter à 5 résultats
                response.getWriter().println("<div class='alert alert-secondary'>");
                response.getWriter().println("<strong>" + route.getTrain().getName() + "</strong><br>");
                response.getWriter().println("Départ: " + route.getDepartureTime() + "<br>");
                response.getWriter().println("Arrivée: " + route.getArrivalTime() + "<br>");
                response.getWriter().println("Prix: " + route.getPrice() + "€<br>");
                response.getWriter().println("Places disponibles: " + route.getAvailableSeats());
                response.getWriter().println("</div>");
                count++;
            }
            
            response.getWriter().println("</div></div>");
            
            // Liens de navigation
            response.getWriter().println("<div class='mt-3'>");
            response.getWriter().println("<a href='" + request.getContextPath() + "/search' class='btn btn-primary me-2'>");
            response.getWriter().println("<i class='fas fa-search'></i> Page de recherche");
            response.getWriter().println("</a>");
            response.getWriter().println("<a href='" + request.getContextPath() + "/' class='btn btn-secondary'>");
            response.getWriter().println("<i class='fas fa-home'></i> Retour à l'accueil");
            response.getWriter().println("</a>");
            response.getWriter().println("</div>");
            
            response.getWriter().println("</div>");
            response.getWriter().println("</body>");
            response.getWriter().println("</html>");
            
        } catch (Exception e) {
            response.getWriter().println("<!DOCTYPE html>");
            response.getWriter().println("<html>");
            response.getWriter().println("<head>");
            response.getWriter().println("<title>Erreur</title>");
            response.getWriter().println("<meta charset='UTF-8'>");
            response.getWriter().println("<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>");
            response.getWriter().println("</head>");
            response.getWriter().println("<body>");
            response.getWriter().println("<div class='container mt-5'>");
            response.getWriter().println("<div class='alert alert-danger'>");
            response.getWriter().println("<h4><i class='fas fa-exclamation-triangle'></i> Erreur !</h4>");
            response.getWriter().println("<p>Erreur lors du test : " + e.getMessage() + "</p>");
            response.getWriter().println("<pre>" + e.getClass().getSimpleName() + "</pre>");
            e.printStackTrace();
            response.getWriter().println("</div>");
            response.getWriter().println("<a href='" + request.getContextPath() + "/' class='btn btn-secondary'>");
            response.getWriter().println("<i class='fas fa-home'></i> Retour à l'accueil");
            response.getWriter().println("</a>");
            response.getWriter().println("</div>");
            response.getWriter().println("</body>");
            response.getWriter().println("</html>");
        }
    }
}
