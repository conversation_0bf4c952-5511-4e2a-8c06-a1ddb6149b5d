package com.trainticket.servlet;

import com.trainticket.dao.impl.RouteDAOImpl;
import com.trainticket.model.Route;
import com.trainticket.util.HibernateUtil;
import jakarta.persistence.EntityManager;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

@WebServlet("/update-dates")
public class UpdateDatesServlet extends HttpServlet {
    
    private RouteDAOImpl routeDAO = new RouteDAOImpl();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        
        try {
            updateRouteDates();
            
            response.getWriter().println("<!DOCTYPE html>");
            response.getWriter().println("<html>");
            response.getWriter().println("<head>");
            response.getWriter().println("<title>Mise à jour des dates</title>");
            response.getWriter().println("<meta charset='UTF-8'>");
            response.getWriter().println("<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>");
            response.getWriter().println("</head>");
            response.getWriter().println("<body>");
            response.getWriter().println("<div class='container mt-5'>");
            response.getWriter().println("<div class='alert alert-success'>");
            response.getWriter().println("<h4><i class='fas fa-check-circle'></i> Mise à jour réussie !</h4>");
            response.getWriter().println("<p>Les dates des trajets ont été mises à jour avec succès.</p>");
            response.getWriter().println("<p>Vous pouvez maintenant tester la recherche de trajets.</p>");
            response.getWriter().println("</div>");
            response.getWriter().println("<div class='mt-3'>");
            response.getWriter().println("<a href='" + request.getContextPath() + "/search' class='btn btn-primary me-2'>");
            response.getWriter().println("<i class='fas fa-search'></i> Tester la recherche");
            response.getWriter().println("</a>");
            response.getWriter().println("<a href='" + request.getContextPath() + "/' class='btn btn-secondary'>");
            response.getWriter().println("<i class='fas fa-home'></i> Retour à l'accueil");
            response.getWriter().println("</a>");
            response.getWriter().println("</div>");
            response.getWriter().println("</div>");
            response.getWriter().println("<script src='https://kit.fontawesome.com/your-kit-id.js'></script>");
            response.getWriter().println("</body>");
            response.getWriter().println("</html>");
            
        } catch (Exception e) {
            response.getWriter().println("<!DOCTYPE html>");
            response.getWriter().println("<html>");
            response.getWriter().println("<head>");
            response.getWriter().println("<title>Erreur</title>");
            response.getWriter().println("<meta charset='UTF-8'>");
            response.getWriter().println("<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>");
            response.getWriter().println("</head>");
            response.getWriter().println("<body>");
            response.getWriter().println("<div class='container mt-5'>");
            response.getWriter().println("<div class='alert alert-danger'>");
            response.getWriter().println("<h4><i class='fas fa-exclamation-triangle'></i> Erreur !</h4>");
            response.getWriter().println("<p>Erreur lors de la mise à jour : " + e.getMessage() + "</p>");
            response.getWriter().println("</div>");
            response.getWriter().println("<a href='" + request.getContextPath() + "/' class='btn btn-secondary'>");
            response.getWriter().println("<i class='fas fa-home'></i> Retour à l'accueil");
            response.getWriter().println("</a>");
            response.getWriter().println("</div>");
            response.getWriter().println("</body>");
            response.getWriter().println("</html>");
        }
    }
    
    private void updateRouteDates() throws Exception {
        EntityManager em = HibernateUtil.getEntityManager();
        
        try {
            em.getTransaction().begin();
            
            // Récupérer tous les trajets
            List<Route> routes = routeDAO.findAll();
            
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime baseDate = now.plusDays(1).withHour(6).withMinute(0).withSecond(0).withNano(0);
            
            for (int i = 0; i < routes.size(); i++) {
                Route route = routes.get(i);
                
                // Calculer la durée originale du trajet
                LocalDateTime originalDeparture = route.getDepartureTime();
                LocalDateTime originalArrival = route.getArrivalTime();
                long durationMinutes = java.time.Duration.between(originalDeparture, originalArrival).toMinutes();
                
                // Calculer la nouvelle date de départ
                int dayOffset = i / 16; // 16 trajets par jour
                int hourOffset = (i % 16) / 2; // 2 trajets par heure
                
                LocalDateTime newDeparture = baseDate
                    .plusDays(dayOffset)
                    .plusHours(hourOffset * 2);
                
                LocalDateTime newArrival = newDeparture.plusMinutes(durationMinutes);
                
                // Mettre à jour les dates
                route.setDepartureTime(newDeparture);
                route.setArrivalTime(newArrival);
                
                em.merge(route);
            }
            
            em.getTransaction().commit();
            
        } catch (Exception e) {
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            throw e;
        } finally {
            em.close();
        }
    }
}
