package com.trainticket.servlet;

import com.trainticket.service.DataInitializationService;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.PrintWriter;

@WebServlet(name = "InitDataServlet", urlPatterns = {"/init-data"})
public class InitDataServlet extends HttpServlet {

    private DataInitializationService dataInitService;

    @Override
    public void init() throws ServletException {
        dataInitService = new DataInitializationService();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Initialisation des Données</title>");
        out.println("<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>");
        out.println("<style>");
        out.println("body { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); min-height: 100vh; }");
        out.println(".card { box-shadow: 0 10px 30px rgba(0,0,0,0.3); border: none; }");
        out.println(".btn-custom { background: linear-gradient(45deg, #ff6b35, #f7931e); border: none; }");
        out.println(".btn-custom:hover { background: linear-gradient(45deg, #e55a2b, #e8851a); }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<div class='container mt-5'>");
        out.println("<div class='row justify-content-center'>");
        out.println("<div class='col-md-8'>");

        out.println("<div class='card'>");
        out.println("<div class='card-header bg-primary text-white text-center'>");
        out.println("<h3>🚂 Initialisation des Données de Test</h3>");
        out.println("</div>");
        out.println("<div class='card-body'>");

        try {
            out.println("<div class='alert alert-info'>");
            out.println("<h5>🔄 Initialisation en cours...</h5>");
            out.println("<p>Création des utilisateurs, trains et trajets de test.</p>");
            out.println("</div>");

            // Initialiser les données
            dataInitService.initializeAllData();

            out.println("<div class='alert alert-success'>");
            out.println("<h5>✅ Initialisation terminée avec succès !</h5>");
            out.println("<p>Les données de test ont été créées dans la base de données.</p>");
            out.println("</div>");

            // Afficher le résumé
            out.println("<div class='card mt-3'>");
            out.println("<div class='card-header bg-info text-white'>");
            out.println("<h5>📊 Résumé des Données Créées</h5>");
            out.println("</div>");
            out.println("<div class='card-body'>");

            out.println("<h6>👥 Comptes de Test Créés :</h6>");
            out.println("<div class='row'>");
            
            out.println("<div class='col-md-4'>");
            out.println("<div class='card border-danger'>");
            out.println("<div class='card-header bg-danger text-white text-center'>");
            out.println("<strong>🔑 ADMINISTRATEUR</strong>");
            out.println("</div>");
            out.println("<div class='card-body text-center'>");
            out.println("<p><strong>Email:</strong> <EMAIL></p>");
            out.println("<p><strong>Mot de passe:</strong> password123</p>");
            out.println("<small class='text-muted'>Accès complet au système</small>");
            out.println("</div>");
            out.println("</div>");
            out.println("</div>");

            out.println("<div class='col-md-4'>");
            out.println("<div class='card border-warning'>");
            out.println("<div class='card-header bg-warning text-dark text-center'>");
            out.println("<strong>👨‍💼 EMPLOYÉ</strong>");
            out.println("</div>");
            out.println("<div class='card-body text-center'>");
            out.println("<p><strong>Email:</strong> <EMAIL></p>");
            out.println("<p><strong>Mot de passe:</strong> password123</p>");
            out.println("<small class='text-muted'>Gestion des réservations</small>");
            out.println("</div>");
            out.println("</div>");
            out.println("</div>");

            out.println("<div class='col-md-4'>");
            out.println("<div class='card border-success'>");
            out.println("<div class='card-header bg-success text-white text-center'>");
            out.println("<strong>👤 CLIENT</strong>");
            out.println("</div>");
            out.println("<div class='card-body text-center'>");
            out.println("<p><strong>Email:</strong> <EMAIL></p>");
            out.println("<p><strong>Mot de passe:</strong> password123</p>");
            out.println("<small class='text-muted'>Réservation de billets</small>");
            out.println("</div>");
            out.println("</div>");
            out.println("</div>");

            out.println("</div>");

            out.println("<div class='mt-4'>");
            out.println("<h6>🚄 Données Créées :</h6>");
            out.println("<ul class='list-group'>");
            out.println("<li class='list-group-item d-flex justify-content-between align-items-center'>");
            out.println("Utilisateurs <span class='badge bg-primary rounded-pill'>8</span>");
            out.println("</li>");
            out.println("<li class='list-group-item d-flex justify-content-between align-items-center'>");
            out.println("Trains <span class='badge bg-success rounded-pill'>9</span>");
            out.println("</li>");
            out.println("<li class='list-group-item d-flex justify-content-between align-items-center'>");
            out.println("Trajets <span class='badge bg-warning rounded-pill'>500+</span>");
            out.println("</li>");
            out.println("</ul>");
            out.println("</div>");

            out.println("</div>");
            out.println("</div>");

            out.println("<div class='text-center mt-4'>");
            out.println("<a href='" + request.getContextPath() + "/login' class='btn btn-custom btn-lg me-3'>");
            out.println("🔐 Se Connecter");
            out.println("</a>");
            out.println("<a href='" + request.getContextPath() + "/test' class='btn btn-outline-light btn-lg me-3'>");
            out.println("🧪 Tester l'Application");
            out.println("</a>");
            out.println("<a href='" + request.getContextPath() + "/' class='btn btn-outline-light btn-lg'>");
            out.println("🏠 Accueil");
            out.println("</a>");
            out.println("</div>");

        } catch (Exception e) {
            out.println("<div class='alert alert-danger'>");
            out.println("<h5>❌ Erreur lors de l'initialisation</h5>");
            out.println("<p><strong>Erreur:</strong> " + e.getMessage() + "</p>");
            out.println("<details>");
            out.println("<summary>Détails techniques</summary>");
            out.println("<pre class='mt-2'>" + getStackTrace(e) + "</pre>");
            out.println("</details>");
            out.println("</div>");

            out.println("<div class='text-center mt-4'>");
            out.println("<a href='" + request.getContextPath() + "/test' class='btn btn-warning'>");
            out.println("🔍 Diagnostiquer le Problème");
            out.println("</a>");
            out.println("</div>");
        }

        out.println("</div>");
        out.println("</div>");
        out.println("</div>");
        out.println("</div>");
        out.println("</div>");
        out.println("</body>");
        out.println("</html>");
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // Rediriger vers GET pour éviter les soumissions multiples
        response.sendRedirect(request.getRequestURI());
    }

    private String getStackTrace(Exception e) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }
}
