<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<c:set var="pageTitle" value="Mon Profil - TrainTickets" scope="request"/>
<jsp:include page="/WEB-INF/views/layout/header.jsp"/>

<div class="row">
    <!-- Informations du profil -->
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="card-title mb-0">
                    <i class="fas fa-user-edit me-2"></i>Modifier mon profil
                </h4>
            </div>
            <div class="card-body">
                <!-- Messages d'erreur/succès -->
                <c:if test="${error != null}">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>${error}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                </c:if>

                <c:if test="${success != null}">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>${success}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                </c:if>

                <!-- Messages flash de session -->
                <c:if test="${sessionScope.flashMessage != null}">
                    <div class="alert alert-${sessionScope.flashType} alert-dismissible fade show" role="alert">
                        <c:choose>
                            <c:when test="${sessionScope.flashType == 'success'}">
                                <i class="fas fa-check-circle me-2"></i>
                            </c:when>
                            <c:otherwise>
                                <i class="fas fa-exclamation-triangle me-2"></i>
                            </c:otherwise>
                        </c:choose>
                        ${sessionScope.flashMessage}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <c:remove var="flashMessage" scope="session"/>
                    <c:remove var="flashType" scope="session"/>
                </c:if>

                <form action="${pageContext.request.contextPath}/user/profile" method="post" id="profileForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="firstName" class="form-label">
                                <i class="fas fa-user me-1"></i>Prénom *
                            </label>
                            <input type="text" class="form-control" id="firstName" name="firstName"
                                   value="${currentUser.firstName}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="lastName" class="form-label">
                                <i class="fas fa-user me-1"></i>Nom *
                            </label>
                            <input type="text" class="form-control" id="lastName" name="lastName"
                                   value="${currentUser.lastName}" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-at me-1"></i>Nom d'utilisateur
                        </label>
                        <input type="text" class="form-control" id="username" name="username"
                               value="${currentUser.username}" readonly>
                        <div class="form-text">Le nom d'utilisateur ne peut pas être modifié</div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email *
                        </label>
                        <input type="email" class="form-control" id="email" name="email"
                               value="${currentUser.email}" required>
                    </div>

                    <div class="mb-3">
                        <label for="phoneNumber" class="form-label">
                            <i class="fas fa-phone me-1"></i>Numéro de téléphone
                        </label>
                        <input type="tel" class="form-control" id="phoneNumber" name="phoneNumber"
                               value="${currentUser.phoneNumber}" placeholder="+33 1 23 45 67 89"
                               pattern="^(\+33|0)[1-9](\d{8})$" title="Format: +33123456789 ou 0123456789">
                        <div class="form-text">Format: +33 1 23 45 67 89 ou 01 23 45 67 89</div>
                    </div>

                    <!-- Informations supplémentaires -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-calendar me-1"></i>Membre depuis
                            </label>
                            <input type="text" class="form-control"
                                   value="<fmt:formatDate value='${currentUser.createdAt}' pattern='dd/MM/yyyy'/>" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-shield-alt me-1"></i>Rôle
                            </label>
                            <input type="text" class="form-control"
                                   value="${currentUser.role.displayName}" readonly>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="saveProfileBtn">
                            <i class="fas fa-save me-2"></i>Enregistrer les modifications
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Changement de mot de passe -->
        <div class="card shadow">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lock me-2"></i>Changer le mot de passe
                </h5>
            </div>
            <div class="card-body">
                <form action="${pageContext.request.contextPath}/user/change-password" method="post" id="passwordForm">
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">
                            <i class="fas fa-key me-1"></i>Mot de passe actuel *
                        </label>
                        <input type="password" class="form-control" id="currentPassword" name="currentPassword" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="newPassword" class="form-label">
                                <i class="fas fa-lock me-1"></i>Nouveau mot de passe *
                            </label>
                            <input type="password" class="form-control" id="newPassword" name="newPassword"
                                   required minlength="6">
                            <div class="form-text">
                                Au moins 6 caractères avec lettres et chiffres
                                <span class="float-end">
                                    Force: <span id="passwordStrength" class="badge bg-secondary">-</span>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirmNewPassword" class="form-label">
                                <i class="fas fa-lock me-1"></i>Confirmer le nouveau mot de passe *
                            </label>
                            <input type="password" class="form-control" id="confirmNewPassword" name="confirmNewPassword" required>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>Changer le mot de passe
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Informations du compte -->
    <div class="col-lg-4">
        <!-- Résumé du compte -->
        <div class="card shadow mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Informations du compte
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center"
                         style="width: 80px; height: 80px; font-size: 2rem;">
                        ${currentUser.firstName.substring(0,1)}${currentUser.lastName.substring(0,1)}
                    </div>
                </div>

                <div class="text-center">
                    <h5>${currentUser.firstName} ${currentUser.lastName}</h5>
                    <p class="text-muted mb-2">${currentUser.email}</p>
                    <span class="badge bg-primary">${currentUser.role.displayName}</span>
                </div>

                <hr>

                <div class="row text-center">
                    <div class="col-4">
                        <h6 class="text-muted">Membre depuis</h6>
                        <p class="mb-0">
                            <fmt:formatDate value="${currentUser.createdAt}" pattern="MMMM yyyy"/>
                        </p>
                    </div>
                    <div class="col-4">
                        <h6 class="text-muted">Réservations</h6>
                        <p class="mb-0">
                            <span class="badge bg-info">
                                <c:choose>
                                    <c:when test="${currentUser.reservations != null}">
                                        ${currentUser.reservations.size()}
                                    </c:when>
                                    <c:otherwise>0</c:otherwise>
                                </c:choose>
                            </span>
                        </p>
                    </div>
                    <div class="col-4">
                        <h6 class="text-muted">Statut</h6>
                        <p class="mb-0">
                            <c:choose>
                                <c:when test="${currentUser.isActive}">
                                    <span class="badge bg-success">Actif</span>
                                </c:when>
                                <c:otherwise>
                                    <span class="badge bg-danger">Inactif</span>
                                </c:otherwise>
                            </c:choose>
                        </p>
                    </div>
                </div>

                <hr>

                <!-- Dernière mise à jour -->
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        Dernière mise à jour:
                        <c:choose>
                            <c:when test="${currentUser.updatedAt != null}">
                                <fmt:formatDate value="${currentUser.updatedAt}" pattern="dd/MM/yyyy à HH:mm"/>
                            </c:when>
                            <c:otherwise>
                                <fmt:formatDate value="${currentUser.createdAt}" pattern="dd/MM/yyyy à HH:mm"/>
                            </c:otherwise>
                        </c:choose>
                    </small>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Actions rapides
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="${pageContext.request.contextPath}/user/dashboard" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>Retour au dashboard
                    </a>
                    <a href="${pageContext.request.contextPath}/user/reservations" class="btn btn-outline-success">
                        <i class="fas fa-ticket-alt me-2"></i>Mes réservations
                    </a>
                    <a href="${pageContext.request.contextPath}/search" class="btn btn-outline-info">
                        <i class="fas fa-search me-2"></i>Rechercher un trajet
                    </a>
                </div>
            </div>
        </div>

        <!-- Préférences -->
        <div class="card shadow">
            <div class="card-header bg-secondary text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>Préférences
                </h6>
            </div>
            <div class="card-body">
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                    <label class="form-check-label" for="emailNotifications">
                        Notifications par email
                    </label>
                </div>
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="smsNotifications">
                    <label class="form-check-label" for="smsNotifications">
                        Notifications par SMS
                    </label>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="promotionalEmails">
                    <label class="form-check-label" for="promotionalEmails">
                        Emails promotionnels
                    </label>
                </div>

                <hr>

                <div class="d-grid">
                    <button type="button" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-save me-2"></i>Sauvegarder les préférences
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire de profil
    const profileForm = document.getElementById('profileForm');
    const saveProfileBtn = document.getElementById('saveProfileBtn');

    profileForm.addEventListener('submit', function(e) {
        const email = document.getElementById('email').value;
        const firstName = document.getElementById('firstName').value;
        const lastName = document.getElementById('lastName').value;
        const phoneNumber = document.getElementById('phoneNumber').value;

        // Validation email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            showAlert('Veuillez entrer une adresse email valide', 'danger');
            return false;
        }

        // Validation nom et prénom
        if (firstName.trim().length < 2 || lastName.trim().length < 2) {
            e.preventDefault();
            showAlert('Le nom et le prénom doivent contenir au moins 2 caractères', 'danger');
            return false;
        }

        // Validation téléphone (optionnel)
        if (phoneNumber && phoneNumber.trim() !== '') {
            const phoneRegex = /^(\+33|0)[1-9](\d{8})$/;
            if (!phoneRegex.test(phoneNumber.replace(/\s/g, ''))) {
                e.preventDefault();
                showAlert('Format de téléphone invalide. Utilisez: +33123456789 ou 0123456789', 'danger');
                return false;
            }
        }

        // Animation du bouton
        saveProfileBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement...';
        saveProfileBtn.disabled = true;
    });

    // Validation du changement de mot de passe
    const passwordForm = document.getElementById('passwordForm');
    passwordForm.addEventListener('submit', function(e) {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmNewPassword').value;

        if (!currentPassword) {
            e.preventDefault();
            showAlert('Veuillez entrer votre mot de passe actuel', 'danger');
            return false;
        }

        if (newPassword !== confirmPassword) {
            e.preventDefault();
            showAlert('Les nouveaux mots de passe ne correspondent pas', 'danger');
            return false;
        }

        // Validation du mot de passe
        const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{6,}$/;
        if (!passwordRegex.test(newPassword)) {
            e.preventDefault();
            showAlert('Le nouveau mot de passe doit contenir au moins 6 caractères avec des lettres et des chiffres', 'danger');
            return false;
        }

        if (currentPassword === newPassword) {
            e.preventDefault();
            showAlert('Le nouveau mot de passe doit être différent de l\'ancien', 'warning');
            return false;
        }
    });

    // Vérification en temps réel des mots de passe
    const confirmPasswordField = document.getElementById('confirmNewPassword');
    const newPasswordField = document.getElementById('newPassword');

    function checkPasswordMatch() {
        const newPassword = newPasswordField.value;
        const confirmPassword = confirmPasswordField.value;

        if (confirmPassword && newPassword !== confirmPassword) {
            confirmPasswordField.setCustomValidity('Les mots de passe ne correspondent pas');
            confirmPasswordField.classList.add('is-invalid');
        } else {
            confirmPasswordField.setCustomValidity('');
            confirmPasswordField.classList.remove('is-invalid');
            if (confirmPassword && newPassword === confirmPassword) {
                confirmPasswordField.classList.add('is-valid');
            }
        }
    }

    confirmPasswordField.addEventListener('input', checkPasswordMatch);
    newPasswordField.addEventListener('input', checkPasswordMatch);

    // Indicateur de force du mot de passe
    newPasswordField.addEventListener('input', function() {
        const password = this.value;
        const strength = getPasswordStrength(password);
        updatePasswordStrengthIndicator(strength);
    });

    // Sauvegarde automatique des préférences
    document.querySelectorAll('.form-check-input').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const preference = this.id;
            const value = this.checked;

            // Animation de sauvegarde
            const label = this.nextElementSibling;
            const originalText = label.textContent;
            label.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>' + originalText;

            // Simulation d'une sauvegarde AJAX
            setTimeout(() => {
                label.innerHTML = '<i class="fas fa-check text-success me-1"></i>' + originalText;
                setTimeout(() => {
                    label.textContent = originalText;
                }, 2000);
            }, 500);

            console.log('Préférence modifiée:', preference, value);
        });
    });

    // Auto-dismiss des alertes après 5 secondes
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(alert => {
            if (alert.querySelector('.btn-close')) {
                alert.querySelector('.btn-close').click();
            }
        });
    }, 5000);
});

// Fonction pour afficher des alertes dynamiques
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.card-body');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    alertContainer.insertBefore(alertDiv, alertContainer.firstChild);

    // Auto-dismiss après 5 secondes
    setTimeout(() => {
        if (alertDiv.querySelector('.btn-close')) {
            alertDiv.querySelector('.btn-close').click();
        }
    }, 5000);
}

// Fonction pour évaluer la force du mot de passe
function getPasswordStrength(password) {
    let strength = 0;
    if (password.length >= 6) strength++;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
}

// Fonction pour mettre à jour l'indicateur de force du mot de passe
function updatePasswordStrengthIndicator(strength) {
    const indicator = document.getElementById('passwordStrength');
    if (!indicator) return;

    const colors = ['danger', 'danger', 'warning', 'warning', 'success', 'success'];
    const texts = ['Très faible', 'Faible', 'Moyen', 'Bon', 'Fort', 'Très fort'];

    indicator.className = `badge bg-${colors[strength]}`;
    indicator.textContent = texts[strength];
}
</script>

<jsp:include page="/WEB-INF/views/layout/footer.jsp"/>
