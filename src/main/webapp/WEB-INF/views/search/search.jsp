<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<c:set var="pageTitle" value="Recherche de trajets - TrainTickets" scope="request"/>
<jsp:include page="/WEB-INF/views/layout/header.jsp"/>

<!-- Formulaire de recherche -->
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h4 class="card-title mb-0">
            <i class="fas fa-search me-2"></i>Rechercher un trajet
        </h4>
    </div>
    <div class="card-body">
        <!-- Messages d'erreur -->
        <c:if test="${error != null}">
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>${error}
            </div>
        </c:if>

        <form action="${pageContext.request.contextPath}/search-routes" method="post">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="departureCity" class="form-label">Ville de départ *</label>
                    <c:choose>
                        <c:when test="${departureCities != null && !empty departureCities}">
                            <select class="form-select" id="departureCity" name="departureCity" required>
                                <option value="">Choisir une ville</option>
                                <c:forEach var="city" items="${departureCities}">
                                    <option value="${city}" ${departureCity == city ? 'selected' : ''}>${city}</option>
                                </c:forEach>
                            </select>
                        </c:when>
                        <c:otherwise>
                            <input type="text" class="form-control" id="departureCity" name="departureCity" 
                                   value="${departureCity}" placeholder="Ex: Paris" required>
                        </c:otherwise>
                    </c:choose>
                </div>
                
                <div class="col-md-3">
                    <label for="arrivalCity" class="form-label">Ville d'arrivée *</label>
                    <c:choose>
                        <c:when test="${arrivalCities != null && !empty arrivalCities}">
                            <select class="form-select" id="arrivalCity" name="arrivalCity" required>
                                <option value="">Choisir une ville</option>
                                <c:forEach var="city" items="${arrivalCities}">
                                    <option value="${city}" ${arrivalCity == city ? 'selected' : ''}>${city}</option>
                                </c:forEach>
                            </select>
                        </c:when>
                        <c:otherwise>
                            <input type="text" class="form-control" id="arrivalCity" name="arrivalCity" 
                                   value="${arrivalCity}" placeholder="Ex: Lyon" required>
                        </c:otherwise>
                    </c:choose>
                </div>
                
                <div class="col-md-2">
                    <label for="departureDate" class="form-label">Date de départ</label>
                    <input type="date" class="form-control" id="departureDate" name="departureDate" 
                           value="${departureDate}">
                </div>
                
                <div class="col-md-2">
                    <label for="passengerCount" class="form-label">Passagers</label>
                    <select class="form-select" id="passengerCount" name="passengerCount">
                        <c:forEach var="i" begin="1" end="10">
                            <option value="${i}" ${passengerCount == i ? 'selected' : ''}>${i}</option>
                        </c:forEach>
                    </select>
                </div>
                
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>Rechercher
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Résultats de recherche -->
<c:if test="${searchPerformed}">
    <div class="card shadow">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Résultats de recherche
                </h5>
                <c:if test="${routes != null && !empty routes}">
                    <span class="badge bg-primary">${routes.size()} trajet(s) trouvé(s)</span>
                </c:if>
            </div>
        </div>
        <div class="card-body">
            <!-- Message de résultats -->
            <c:if test="${resultsMessage != null}">
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>${resultsMessage}
                </div>
            </c:if>

            <!-- Statistiques des résultats -->
            <c:if test="${routes != null && !empty routes}">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-euro-sign text-success mb-2"></i>
                                <h6 class="card-title">Prix</h6>
                                <p class="card-text">
                                    <fmt:formatNumber value="${minPrice}" type="currency" currencySymbol="€"/> -
                                    <fmt:formatNumber value="${maxPrice}" type="currency" currencySymbol="€"/>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-clock text-primary mb-2"></i>
                                <h6 class="card-title">Durée</h6>
                                <p class="card-text">${minDuration} - ${maxDuration}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-users text-info mb-2"></i>
                                <h6 class="card-title">Places totales</h6>
                                <p class="card-text">${totalAvailableSeats} disponibles</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-train text-warning mb-2"></i>
                                <h6 class="card-title">Types de trains</h6>
                                <p class="card-text">${trainTypesCount} type(s)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>
            
            <!-- Message aucun résultat -->
            <c:if test="${noResultsMessage != null}">
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>${noResultsMessage}
                    <hr>
                    <p class="mb-0">Suggestions :</p>
                    <ul class="mb-0">
                        <li>Vérifiez l'orthographe des villes</li>
                        <li>Essayez une date différente</li>
                        <li>Réduisez le nombre de passagers</li>
                    </ul>
                </div>
            </c:if>

            <!-- Liste des trajets -->
            <c:if test="${routes != null && !empty routes}">
                <div class="row">
                    <c:forEach var="route" items="${routes}">
                        <div class="col-12 mb-3">
                            <div class="card border">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <h6 class="mb-1">
                                                <i class="fas fa-train text-primary me-2"></i>
                                                ${route.train.name}
                                            </h6>
                                            <small class="text-muted">${route.train.trainType.displayName}</small>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center">
                                                <div class="text-center">
                                                    <strong>${route.departureCity}</strong><br>
                                                    <small class="text-muted">
                                                        <fmt:formatDate value="${route.departureTime}" pattern="HH:mm"/>
                                                    </small>
                                                </div>
                                                <div class="mx-3">
                                                    <i class="fas fa-arrow-right text-primary"></i>
                                                </div>
                                                <div class="text-center">
                                                    <strong>${route.arrivalCity}</strong><br>
                                                    <small class="text-muted">
                                                        <fmt:formatDate value="${route.arrivalTime}" pattern="HH:mm"/>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-2 text-center">
                                            <div class="text-success">
                                                <strong><fmt:formatNumber value="${route.price}" type="currency" currencySymbol="€"/></strong>
                                            </div>
                                            <small class="text-muted">par personne</small>
                                        </div>
                                        
                                        <div class="col-md-2 text-center">
                                            <div class="text-info">
                                                <i class="fas fa-users me-1"></i>
                                                <strong>${route.availableSeats}</strong>
                                            </div>
                                            <small class="text-muted">places libres</small>
                                            <br>
                                            <c:choose>
                                                <c:when test="${route.availabilityStatus == 'Complet'}">
                                                    <span class="badge bg-danger">${route.availabilityStatus}</span>
                                                </c:when>
                                                <c:when test="${route.availabilityStatus == 'Peu de places'}">
                                                    <span class="badge bg-warning">${route.availabilityStatus}</span>
                                                </c:when>
                                                <c:when test="${route.availabilityStatus == 'Places limitées'}">
                                                    <span class="badge bg-info">${route.availabilityStatus}</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span class="badge bg-success">${route.availabilityStatus}</span>
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                        
                                        <div class="col-md-1 text-center">
                                            <c:choose>
                                                <c:when test="${route.availableSeats >= passengerCount}">
                                                    <a href="${pageContext.request.contextPath}/reservation/book?routeId=${route.id}&passengerCount=${passengerCount}" 
                                                       class="btn btn-primary btn-sm">
                                                        <i class="fas fa-ticket-alt me-1"></i>Réserver
                                                    </a>
                                                </c:when>
                                                <c:otherwise>
                                                    <button class="btn btn-secondary btn-sm" disabled>
                                                        <i class="fas fa-times me-1"></i>Complet
                                                    </button>
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                    
                                    <!-- Informations supplémentaires -->
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                <fmt:formatDate value="${route.departureTime}" pattern="EEEE dd MMMM yyyy"/>
                                                •
                                                <i class="fas fa-clock me-1"></i>
                                                Durée: ${route.formattedDuration}
                                                •
                                                <i class="fas fa-info-circle me-1"></i>
                                                ${route.availabilityStatus}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:forEach>
                </div>
            </c:if>
        </div>
    </div>
</c:if>

<script>
// Définir la date minimale à aujourd'hui
document.getElementById('departureDate').min = new Date().toISOString().split('T')[0];

// Validation du formulaire
document.querySelector('form').addEventListener('submit', function(e) {
    const departureCity = document.getElementById('departureCity').value;
    const arrivalCity = document.getElementById('arrivalCity').value;
    
    if (departureCity === arrivalCity) {
        e.preventDefault();
        alert('La ville de départ et d\'arrivée ne peuvent pas être identiques');
        return false;
    }
});
</script>

<jsp:include page="/WEB-INF/views/layout/footer.jsp"/>
