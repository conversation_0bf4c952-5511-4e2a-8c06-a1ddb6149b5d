<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page isErrorPage="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erreur Serveur - TrainTickets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #1e40af;
            --accent-color: #312e81;
            --orange-color: #c2410c;
            --orange-light: #ea580c;
            --orange-dark: #9a3412;
            --dark-color: #0f172a;
            --light-color: #f8fafc;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            color: white;
        }

        .error-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .error-icon {
            font-size: 5rem;
            color: var(--orange-light);
            margin-bottom: 2rem;
        }

        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .error-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .btn-home {
            background: var(--orange-color);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(194, 65, 12, 0.3);
        }

        .btn-home:hover {
            background: var(--orange-dark);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(154, 52, 18, 0.4);
        }

        .error-details {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }

        .btn-toggle {
            background: transparent;
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }

        .btn-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="error-container">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>

                    <h1 class="error-title">Erreur Serveur</h1>
                    <p class="error-subtitle">
                        Oops ! Une erreur technique s'est produite.<br>
                        Nos équipes ont été notifiées et travaillent à résoudre le problème.
                    </p>

                    <!-- Détails de l'erreur (masqués par défaut) -->
                    <div id="errorDetails" class="error-details" style="display: none;">
                        <strong>Détails techniques :</strong><br>
                        <c:if test="${pageContext.exception != null}">
                            <strong>Exception :</strong> ${pageContext.exception.class.simpleName}<br>
                            <strong>Message :</strong> ${pageContext.exception.message}<br>
                            <strong>URI :</strong> ${pageContext.request.requestURI}<br>
                        </c:if>
                        <c:if test="${error != null}">
                            <strong>Erreur :</strong> ${error}<br>
                        </c:if>
                        <strong>Timestamp :</strong> <%= new java.util.Date() %><br>
                        <strong>Session ID :</strong> ${pageContext.session.id}
                    </div>

                    <div class="mt-4">
                        <a href="${pageContext.request.contextPath}/" class="btn-home me-3">
                            <i class="fas fa-home me-2"></i>Retour à l'Accueil
                        </a>
                        <a href="${pageContext.request.contextPath}/init-data" class="btn-home" style="background: var(--secondary-color);">
                            <i class="fas fa-database me-2"></i>Initialiser les Données
                        </a>
                    </div>

                    <div class="mt-3">
                        <button class="btn-toggle" onclick="toggleErrorDetails()">
                            <i class="fas fa-code me-1"></i>Afficher les détails techniques
                        </button>
                    </div>

                    <div class="mt-4">
                        <small style="opacity: 0.7;">
                            Si le problème persiste, contactez notre support :<br>
                            <i class="fas fa-envelope me-1"></i><EMAIL><br>
                            <i class="fas fa-phone me-1"></i>+33 1 23 45 67 89
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleErrorDetails() {
            const details = document.getElementById('errorDetails');
            const button = document.querySelector('.btn-toggle');

            if (details.style.display === 'none') {
                details.style.display = 'block';
                button.innerHTML = '<i class="fas fa-code me-1"></i>Masquer les détails techniques';
            } else {
                details.style.display = 'none';
                button.innerHTML = '<i class="fas fa-code me-1"></i>Afficher les détails techniques';
            }
        }

        // Log de l'erreur côté client
        console.error('Erreur 500 détectée sur la page:', window.location.href);

        // Redirection automatique après 30 secondes (optionnel)
        setTimeout(function() {
            if (confirm('Voulez-vous être redirigé vers la page d\'accueil ?')) {
                window.location.href = '${pageContext.request.contextPath}/';
            }
        }, 30000);
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
